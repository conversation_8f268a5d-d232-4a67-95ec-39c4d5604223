{"version": 3, "file": "dmgUtil.js", "sourceRoot": "", "sources": ["../src/dmgUtil.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAE1C,sDAAyD;AACzD,6BAA4B;AAE5B,6BAAiC;AAAxB,gGAAA,SAAS,OAAA;AAElB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAEvC,SAAgB,kBAAkB;IAChC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;AACrC,CAAC;AAFD,gDAEC;AAED,SAAgB,gBAAgB;IAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;AAClC,CAAC;AAFD,4CAEC;AAEM,KAAK,UAAU,gBAAgB,CAAC,OAAe,EAAE,SAAkB,EAAE,IAAwB;IAClG,sCAAsC;IACtC,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC,CAAA;IACnD,IAAI,SAAS,EAAE,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IACzB,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAClB,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAI,EAAC,SAAS,EAAE,IAAI,CAAC,CAAA;IAChD,MAAM,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IACrF,MAAM,MAAM,GAAG,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;IACzF,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,iBAAiB,YAAY,EAAE,CAAC,CAAA;IAClD,CAAC;IAED,OAAO,MAAM,IAAA,wBAAc,EAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;AAC3D,CAAC;AAhBD,4CAgBC;AAEM,KAAK,UAAU,MAAM,CAAC,IAAY;IACvC,IAAI,CAAC;QACH,MAAM,IAAA,mBAAI,EAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAA;IACnD,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,IAAA,oBAAK,EAAC,GAAG,EAAE,CAAC,IAAA,mBAAI,EAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;IACxF,CAAC;AACH,CAAC;AAND,wBAMC;AAEM,KAAK,UAAU,iBAAiB,CAAC,QAA+B;IACrE,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAA;IAChD,IAAI,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAA;IACjE,CAAC;SAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAA;IAChE,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,iBAAiB,CAAC,CAAA;IAC3D,CAAC;AACH,CAAC;AATD,8CASC;AAED,gBAAgB;AAChB,SAAgB,eAAe,CAAC,IAAY;IAC1C,OAAO,CACL,MAAM;QACN,IAAI;aACD,KAAK,CAAC,UAAU,CAAE;aAClB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACzC,IAAI,CAAC,SAAS,CAAC;QAClB,GAAG,CACJ,CAAA;AACH,CAAC;AATD,0CASC", "sourcesContent": ["import { exec, retry } from \"builder-util\"\nimport { PlatformPackager } from \"app-builder-lib\"\nimport { executeFinally } from \"builder-util/out/promise\"\nimport * as path from \"path\"\n\nexport { DmgTarget } from \"./dmg\"\n\nconst root = path.join(__dirname, \"..\")\n\nexport function getDmgTemplatePath() {\n  return path.join(root, \"templates\")\n}\n\nexport function getDmgVendorPath() {\n  return path.join(root, \"vendor\")\n}\n\nexport async function attachAndExecute(dmgPath: string, readWrite: boolean, task: () => Promise<any>) {\n  //noinspection SpellCheckingInspection\n  const args = [\"attach\", \"-noverify\", \"-noautoopen\"]\n  if (readWrite) {\n    args.push(\"-readwrite\")\n  }\n\n  args.push(dmgPath)\n  const attachResult = await exec(\"hdiutil\", args)\n  const deviceResult = attachResult == null ? null : /^(\\/dev\\/\\w+)/.exec(attachResult)\n  const device = deviceResult == null || deviceResult.length !== 2 ? null : deviceResult[1]\n  if (device == null) {\n    throw new Error(`Cannot mount: ${attachResult}`)\n  }\n\n  return await executeFinally(task(), () => detach(device))\n}\n\nexport async function detach(name: string) {\n  try {\n    await exec(\"hdiutil\", [\"detach\", \"-quiet\", name])\n  } catch (e: any) {\n    await retry(() => exec(\"hdiutil\", [\"detach\", \"-force\", \"-debug\", name]), 5, 1000, 500)\n  }\n}\n\nexport async function computeBackground(packager: PlatformPackager<any>): Promise<string> {\n  const resourceList = await packager.resourceList\n  if (resourceList.includes(\"background.tiff\")) {\n    return path.join(packager.buildResourcesDir, \"background.tiff\")\n  } else if (resourceList.includes(\"background.png\")) {\n    return path.join(packager.buildResourcesDir, \"background.png\")\n  } else {\n    return path.join(getDmgTemplatePath(), \"background.tiff\")\n  }\n}\n\n/** @internal */\nexport function serializeString(data: string) {\n  return (\n    '  $\"' +\n    data\n      .match(/.{1,32}/g)!\n      .map(it => it.match(/.{1,4}/g)!.join(\" \"))\n      .join('\"\\n  $\"') +\n    '\"'\n  )\n}\n"]}