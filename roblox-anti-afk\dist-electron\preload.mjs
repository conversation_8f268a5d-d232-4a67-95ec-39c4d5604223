"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("ipcRenderer", {
  on(...args) {
    const [channel, listener] = args;
    return electron.ipcRenderer.on(channel, (event, ...args2) => listener(event, ...args2));
  },
  off(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.off(channel, ...omit);
  },
  send(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.send(channel, ...omit);
  },
  invoke(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.invoke(channel, ...omit);
  }
  // You can expose other APTs you need here.
  // ...
});
electron.contextBridge.exposeInMainWorld("electronAPI", {
  resizeWindow: (width, height) => {
    electron.ipcRenderer.send("resize-window", { width, height });
  },
  // Roblox detection and anti-AFK functions
  getRobloxInstances: () => electron.ipcRenderer.invoke("get-roblox-instances"),
  startAntiAfk: (instanceId) => electron.ipcRenderer.invoke("start-anti-afk", instanceId),
  stopAntiAfk: (instanceId) => electron.ipcRenderer.invoke("stop-anti-afk", instanceId),
  toggleAntiAfk: (instanceId) => electron.ipcRenderer.invoke("toggle-anti-afk", instanceId),
  onRobloxInstancesUpdated: (callback) => electron.ipcRenderer.on("roblox-instances-updated", (_event, instances) => callback(instances))
});
