import { app, BrowserWindow, globalShortcut, ipcMain, shell } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import { exec, spawn } from 'node:child_process'
import { promisify } from 'node:util'

const execAsync = promisify(exec)

const require = createRequire(import.meta.url)

// Anti-AFK state management
interface RobloxInstance {
  id: string
  name: string
  pid: number
  windowTitle: string
  isActive: boolean
}

let robloxInstances: RobloxInstance[] = []
let antiAfkIntervals: Map<string, NodeJS.Timeout> = new Map()
let lastActiveWindow: string | null = null

// Function to detect Roblox processes
async function detectRobloxInstances(): Promise<RobloxInstance[]> {
  console.log('Detecting Roblox instances...')

  try {
    if (process.platform === 'win32') {
      // Windows: Use tasklist to get Roblox processes
      const { stdout } = await execAsync('tasklist /fi "imagename eq RobloxPlayerBeta.exe" /fo csv')
      console.log('Tasklist output:', stdout)

      const lines = stdout.split('\n').slice(1) // Skip header
      const instances: RobloxInstance[] = []

      for (const line of lines) {
        if (line.trim() && !line.includes('INFO: No tasks')) {
          const parts = line.split('","').map(part => part.replace(/"/g, ''))
          console.log('Processing line parts:', parts)

          if (parts.length >= 2) {
            const pid = parseInt(parts[1])
            if (!isNaN(pid)) {
              console.log(`Found Roblox instance with PID: ${pid}`)

              // Get window title for this PID
              let windowTitle = `Roblox Player (PID ${pid})`
              try {
                const { stdout: titleOutput } = await execAsync(`powershell "Get-Process -Id ${pid} | Select-Object MainWindowTitle | Format-Table -HideTableHeaders"`)
                const title = titleOutput.trim()
                if (title && title !== '' && !title.includes('MainWindowTitle')) {
                  windowTitle = title
                }
              } catch (e) {
                console.log(`Could not get window title for PID ${pid}:`, e)
              }

              instances.push({
                id: `roblox_${pid}`,
                name: 'Roblox Player',
                pid: pid,
                windowTitle: windowTitle,
                isActive: antiAfkIntervals.has(`roblox_${pid}`)
              })
            }
          }
        }
      }

      console.log(`Found ${instances.length} Roblox instances:`, instances)
      return instances
    } else {
      // macOS/Linux: Use ps command
      const { stdout } = await execAsync('ps aux | grep -i roblox | grep -v grep')
      const lines = stdout.split('\n').filter(line => line.trim())

      return lines.map((line, index) => {
        const parts = line.trim().split(/\s+/)
        const pid = parseInt(parts[1])

        return {
          id: `roblox_${pid}`,
          name: 'Roblox Player',
          pid: pid,
          windowTitle: `Roblox Player (PID ${pid})`,
          isActive: antiAfkIntervals.has(`roblox_${pid}`)
        }
      })
    }
  } catch (error) {
    console.log('Error detecting Roblox instances:', error)

    // Return some dummy data for testing if no real instances found
    return [
      {
        id: 'roblox_12345',
        name: 'Roblox Player',
        pid: 12345,
        windowTitle: 'Roblox Player (PID 12345)',
        isActive: false
      },
      {
        id: 'roblox_67891',
        name: 'Roblox Player',
        pid: 67891,
        windowTitle: 'Roblox Player (PID 67891)',
        isActive: false
      },
      {
        id: 'roblox_54321',
        name: 'Roblox Player',
        pid: 54321,
        windowTitle: 'Roblox Player (PID 54321)',
        isActive: false
      }
    ]
  }
}

// Function to get currently active window
async function getCurrentActiveWindow(): Promise<string | null> {
  try {
    if (process.platform === 'win32') {
      const { stdout } = await execAsync('powershell "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Application]::OpenForms | Select-Object Text"')
      return stdout.trim() || null
    }
    return null
  } catch (error) {
    return null
  }
}

// Function to focus a Roblox window by PID
async function focusRobloxWindow(pid: number): Promise<boolean> {
  try {
    if (process.platform === 'win32') {
      // Store current active window before switching
      lastActiveWindow = await getCurrentActiveWindow()

      // Focus the Roblox window using PowerShell
      await execAsync(`powershell "Add-Type -AssemblyName Microsoft.VisualBasic; Add-Type -AssemblyName System.Windows.Forms; $p = Get-Process -Id ${pid}; if ($p.MainWindowHandle -ne 0) { [Microsoft.VisualBasic.Interaction]::AppActivate($p.Id) }"`)

      return true
    } else {
      // macOS/Linux implementation would go here
      return false
    }
  } catch (error) {
    console.log('Error focusing Roblox window:', error)
    return false
  }
}

// Function to send jump command (Space key)
async function sendJumpCommand(): Promise<void> {
  try {
    if (process.platform === 'win32') {
      // Send space key using PowerShell
      await execAsync('powershell "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\' \')"')
    }
  } catch (error) {
    console.log('Error sending jump command:', error)
  }
}

// Function to restore previous window
async function restorePreviousWindow(): Promise<void> {
  try {
    if (process.platform === 'win32' && lastActiveWindow) {
      // Try to restore the previous window
      await execAsync(`powershell "Get-Process | Where-Object {$_.MainWindowTitle -like '*${lastActiveWindow}*'} | ForEach-Object { [Microsoft.VisualBasic.Interaction]::AppActivate($_.Id) }"`)
    }
  } catch (error) {
    console.log('Error restoring previous window:', error)
  }
}

// Anti-AFK function for a single instance
async function performAntiAfk(instanceId: string, pid: number): Promise<void> {
  console.log(`Performing anti-AFK for instance ${instanceId} (PID: ${pid})`)

  // Focus the Roblox window
  const focused = await focusRobloxWindow(pid)
  if (!focused) {
    console.log('Failed to focus Roblox window')
    return
  }

  // Wait a moment for window to focus
  await new Promise(resolve => setTimeout(resolve, 500))

  // Send jump command
  await sendJumpCommand()

  // Wait a moment before restoring
  await new Promise(resolve => setTimeout(resolve, 200))

  // Restore previous window
  await restorePreviousWindow()

  console.log(`Anti-AFK completed for instance ${instanceId}`)
}

// Start anti-AFK for an instance
function startAntiAfk(instanceId: string, pid: number): void {
  // Clear existing interval if any
  if (antiAfkIntervals.has(instanceId)) {
    clearInterval(antiAfkIntervals.get(instanceId)!)
  }

  // Start new interval (10 seconds for testing)
  const interval = setInterval(() => {
    performAntiAfk(instanceId, pid)
  }, 10 * 1000) // 10 seconds

  antiAfkIntervals.set(instanceId, interval)
  console.log(`Started anti-AFK for instance ${instanceId} (PID: ${pid})`)
}

// Stop anti-AFK for an instance
function stopAntiAfk(instanceId: string): void {
  if (antiAfkIntervals.has(instanceId)) {
    clearInterval(antiAfkIntervals.get(instanceId)!)
    antiAfkIntervals.delete(instanceId)
    console.log(`Stopped anti-AFK for instance ${instanceId}`)
  }
}

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  console.log('=== CREATING WINDOW ===')
  win = new BrowserWindow({
    width: 420,
    height: 480,
    show: true,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    autoHideMenuBar: true,
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
      nodeIntegration: false,
      contextIsolation: true,
      experimentalFeatures: true,
    },
  })



  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', async () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())

    // Force show window immediately for testing
    console.log('Window loaded, showing immediately...')
    win?.show()
    win?.focus()
    win?.setAlwaysOnTop(true)

    // Try to set rounded corners after window is shown
    try {
      // Inject CSS to force rounded corners on all platforms
      win?.webContents.insertCSS(`
        html, body, #root, .app {
          border-radius: 20px !important;
          overflow: hidden !important;
          -webkit-clip-path: inset(0 round 20px) !important;
          clip-path: inset(0 round 20px) !important;
        }
      `)
    } catch (e) {
      console.log('Could not inject CSS for rounded corners:', e)
    }

    // Initial Roblox detection
    console.log('Performing initial Roblox detection...')
    robloxInstances = await detectRobloxInstances()
    console.log('Initial instances found:', robloxInstances)
  })



  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }

  // Register global hotkey to toggle window visibility
  globalShortcut.register('Alt+F9', () => {
    if (win) {
      if (win.isVisible()) {
        // Fade out animation
        win.setOpacity(0)
        setTimeout(() => {
          win?.hide()
          win?.setOpacity(1)
        }, 150)
      } else {
        // Fade in animation
        win.setOpacity(0)
        win.show()
        win.focus()

        // Animate opacity from 0 to 1
        let opacity = 0
        const fadeIn = setInterval(() => {
          opacity += 0.1
          if (opacity >= 1) {
            opacity = 1
            clearInterval(fadeIn)
          }
          win?.setOpacity(opacity)
        }, 15)
      }
    }
  })
}

// IPC handlers for Roblox detection and anti-AFK
ipcMain.handle('get-roblox-instances', async () => {
  console.log('=== IPC: get-roblox-instances called ===')

  try {
    robloxInstances = await detectRobloxInstances()
    console.log('IPC: detectRobloxInstances returned:', robloxInstances)
    console.log('IPC: returning instances count:', robloxInstances.length)
    return robloxInstances
  } catch (error) {
    console.error('IPC: Error in get-roblox-instances:', error)
    // Return dummy data if detection fails
    const dummyInstances = [
      {
        id: 'roblox_12345',
        name: 'Roblox Player',
        pid: 12345,
        windowTitle: 'Roblox Player (PID 12345)',
        isActive: false
      },
      {
        id: 'roblox_67891',
        name: 'Roblox Player',
        pid: 67891,
        windowTitle: 'Roblox Player (PID 67891)',
        isActive: true
      }
    ]
    console.log('IPC: returning dummy instances:', dummyInstances)
    return dummyInstances
  }
})

ipcMain.handle('start-anti-afk', async (event, instanceId: string) => {
  const instance = robloxInstances.find(inst => inst.id === instanceId)
  if (instance) {
    startAntiAfk(instanceId, instance.pid)
    instance.isActive = true
    return { success: true, message: `Started anti-AFK for ${instance.name}` }
  }
  return { success: false, message: 'Instance not found' }
})

ipcMain.handle('stop-anti-afk', async (event, instanceId: string) => {
  const instance = robloxInstances.find(inst => inst.id === instanceId)
  if (instance) {
    stopAntiAfk(instanceId)
    instance.isActive = false
    return { success: true, message: `Stopped anti-AFK for ${instance.name}` }
  }
  return { success: false, message: 'Instance not found' }
})

ipcMain.handle('toggle-anti-afk', async (event, instanceId: string) => {
  const instance = robloxInstances.find(inst => inst.id === instanceId)
  if (instance) {
    if (instance.isActive) {
      stopAntiAfk(instanceId)
      instance.isActive = false
      return { success: true, message: `Stopped anti-AFK for ${instance.name}`, isActive: false }
    } else {
      startAntiAfk(instanceId, instance.pid)
      instance.isActive = true
      return { success: true, message: `Started anti-AFK for ${instance.name}`, isActive: true }
    }
  }
  return { success: false, message: 'Instance not found', isActive: false }
})

// Auto-refresh Roblox instances every 10 seconds
setInterval(async () => {
  const newInstances = await detectRobloxInstances()

  // Update existing instances and preserve active states
  const updatedInstances = newInstances.map(newInst => {
    const existing = robloxInstances.find(inst => inst.pid === newInst.pid)
    if (existing) {
      return { ...newInst, isActive: existing.isActive }
    }
    return newInst
  })

  // Clean up intervals for instances that no longer exist
  const currentPids = new Set(updatedInstances.map(inst => inst.pid))
  for (const [instanceId, interval] of antiAfkIntervals.entries()) {
    const pid = parseInt(instanceId.replace('roblox_', ''))
    if (!currentPids.has(pid)) {
      clearInterval(interval)
      antiAfkIntervals.delete(instanceId)
      console.log(`Cleaned up anti-AFK for removed instance ${instanceId}`)
    }
  }

  robloxInstances = updatedInstances

  // Send updated instances to renderer
  if (win && !win.isDestroyed()) {
    win.webContents.send('roblox-instances-updated', robloxInstances)
  }
}, 10000) // 10 seconds

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    globalShortcut.unregisterAll()
    app.quit()
    win = null
  }
})

app.on('will-quit', () => {
  // Unregister all shortcuts.
  globalShortcut.unregisterAll()
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.whenReady().then(createWindow)
