import { app, BrowserWindow, globalShortcut, ipcMain } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    width: 420,
    height: 320,
    show: false,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    autoHideMenuBar: true,
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
      nodeIntegration: false,
      contextIsolation: true,
      experimentalFeatures: true,
    },
  })



  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
    win?.show() // Show window after content loads

    // Try to set rounded corners after window is shown
    try {
      // Inject CSS to force rounded corners on all platforms
      win?.webContents.insertCSS(`
        html, body, #root, .app {
          border-radius: 20px !important;
          overflow: hidden !important;
          -webkit-clip-path: inset(0 round 20px) !important;
          clip-path: inset(0 round 20px) !important;
        }
      `)
    } catch (e) {
      console.log('Could not inject CSS for rounded corners:', e)
    }
  })



  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }

  // Register global hotkey to toggle window visibility
  globalShortcut.register('Alt+F9', () => {
    if (win) {
      if (win.isVisible()) {
        // Fade out animation
        win.setOpacity(0)
        setTimeout(() => {
          win?.hide()
          win?.setOpacity(1)
        }, 150)
      } else {
        // Fade in animation
        win.setOpacity(0)
        win.show()
        win.focus()

        // Animate opacity from 0 to 1
        let opacity = 0
        const fadeIn = setInterval(() => {
          opacity += 0.1
          if (opacity >= 1) {
            opacity = 1
            clearInterval(fadeIn)
          }
          win?.setOpacity(opacity)
        }, 15)
      }
    }
  })
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    globalShortcut.unregisterAll()
    app.quit()
    win = null
  }
})

app.on('will-quit', () => {
  // Unregister all shortcuts.
  globalShortcut.unregisterAll()
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.whenReady().then(createWindow)
