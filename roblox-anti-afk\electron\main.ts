import { app, BrowserWindow, globalShortcut, ipcMain, shell } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import { exec, spawn } from 'node:child_process'
import { promisify } from 'node:util'

const execAsync = promisify(exec)

const require = createRequire(import.meta.url)

// Anti-AFK state management
interface RobloxInstance {
  id: string
  name: string
  pid: number
  windowTitle: string
  isActive: boolean
}

let robloxInstances: RobloxInstance[] = []
let antiAfkIntervals: Map<string, NodeJS.Timeout> = new Map()
let lastActiveWindow: string | null = null

// Function to detect Roblox processes
async function detectRobloxInstances(): Promise<RobloxInstance[]> {
  try {
    if (process.platform === 'win32') {
      // Windows: Use tasklist and wmic to get Roblox processes
      const { stdout } = await execAsync('tasklist /fi "imagename eq RobloxPlayerBeta.exe" /fo csv')
      const lines = stdout.split('\n').slice(1) // Skip header

      const instances: RobloxInstance[] = []

      for (const line of lines) {
        if (line.trim()) {
          const parts = line.split('","').map(part => part.replace(/"/g, ''))
          if (parts.length >= 2) {
            const pid = parseInt(parts[1])
            if (!isNaN(pid)) {
              // Get window title for this PID
              try {
                const { stdout: titleOutput } = await execAsync(`powershell "Get-Process -Id ${pid} | Select-Object MainWindowTitle | Format-Table -HideTableHeaders"`)
                const windowTitle = titleOutput.trim() || `Roblox Player (PID ${pid})`

                instances.push({
                  id: `roblox_${pid}`,
                  name: 'Roblox Player',
                  pid: pid,
                  windowTitle: windowTitle,
                  isActive: antiAfkIntervals.has(`roblox_${pid}`)
                })
              } catch (e) {
                // Fallback if we can't get window title
                instances.push({
                  id: `roblox_${pid}`,
                  name: 'Roblox Player',
                  pid: pid,
                  windowTitle: `Roblox Player (PID ${pid})`,
                  isActive: antiAfkIntervals.has(`roblox_${pid}`)
                })
              }
            }
          }
        }
      }

      return instances
    } else {
      // macOS/Linux: Use ps command
      const { stdout } = await execAsync('ps aux | grep -i roblox | grep -v grep')
      const lines = stdout.split('\n').filter(line => line.trim())

      return lines.map((line, index) => {
        const parts = line.trim().split(/\s+/)
        const pid = parseInt(parts[1])

        return {
          id: `roblox_${pid}`,
          name: 'Roblox Player',
          pid: pid,
          windowTitle: `Roblox Player (PID ${pid})`,
          isActive: antiAfkIntervals.has(`roblox_${pid}`)
        }
      })
    }
  } catch (error) {
    console.log('No Roblox instances found or error detecting:', error)
    return []
  }
}

// Function to get currently active window
async function getCurrentActiveWindow(): Promise<string | null> {
  try {
    if (process.platform === 'win32') {
      const { stdout } = await execAsync('powershell "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Application]::OpenForms | Select-Object Text"')
      return stdout.trim() || null
    }
    return null
  } catch (error) {
    return null
  }
}

// Function to focus a Roblox window by PID
async function focusRobloxWindow(pid: number): Promise<boolean> {
  try {
    if (process.platform === 'win32') {
      // Store current active window before switching
      lastActiveWindow = await getCurrentActiveWindow()

      // Focus the Roblox window using PowerShell
      await execAsync(`powershell "Add-Type -AssemblyName Microsoft.VisualBasic; Add-Type -AssemblyName System.Windows.Forms; $p = Get-Process -Id ${pid}; if ($p.MainWindowHandle -ne 0) { [Microsoft.VisualBasic.Interaction]::AppActivate($p.Id) }"`)

      return true
    } else {
      // macOS/Linux implementation would go here
      return false
    }
  } catch (error) {
    console.log('Error focusing Roblox window:', error)
    return false
  }
}

// Function to send jump command (Space key)
async function sendJumpCommand(): Promise<void> {
  try {
    if (process.platform === 'win32') {
      // Send space key using PowerShell
      await execAsync('powershell "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\' \')"')
    }
  } catch (error) {
    console.log('Error sending jump command:', error)
  }
}

// Function to restore previous window
async function restorePreviousWindow(): Promise<void> {
  try {
    if (process.platform === 'win32' && lastActiveWindow) {
      // Try to restore the previous window
      await execAsync(`powershell "Get-Process | Where-Object {$_.MainWindowTitle -like '*${lastActiveWindow}*'} | ForEach-Object { [Microsoft.VisualBasic.Interaction]::AppActivate($_.Id) }"`)
    }
  } catch (error) {
    console.log('Error restoring previous window:', error)
  }
}

// Anti-AFK function for a single instance
async function performAntiAfk(instanceId: string, pid: number): Promise<void> {
  console.log(`Performing anti-AFK for instance ${instanceId} (PID: ${pid})`)

  // Focus the Roblox window
  const focused = await focusRobloxWindow(pid)
  if (!focused) {
    console.log('Failed to focus Roblox window')
    return
  }

  // Wait a moment for window to focus
  await new Promise(resolve => setTimeout(resolve, 500))

  // Send jump command
  await sendJumpCommand()

  // Wait a moment before restoring
  await new Promise(resolve => setTimeout(resolve, 200))

  // Restore previous window
  await restorePreviousWindow()

  console.log(`Anti-AFK completed for instance ${instanceId}`)
}

// Start anti-AFK for an instance
function startAntiAfk(instanceId: string, pid: number): void {
  // Clear existing interval if any
  if (antiAfkIntervals.has(instanceId)) {
    clearInterval(antiAfkIntervals.get(instanceId)!)
  }

  // Start new interval (8 minutes = 480000ms)
  const interval = setInterval(() => {
    performAntiAfk(instanceId, pid)
  }, 8 * 60 * 1000) // 8 minutes

  antiAfkIntervals.set(instanceId, interval)
  console.log(`Started anti-AFK for instance ${instanceId} (PID: ${pid})`)
}

// Stop anti-AFK for an instance
function stopAntiAfk(instanceId: string): void {
  if (antiAfkIntervals.has(instanceId)) {
    clearInterval(antiAfkIntervals.get(instanceId)!)
    antiAfkIntervals.delete(instanceId)
    console.log(`Stopped anti-AFK for instance ${instanceId}`)
  }
}

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    width: 420,
    height: 480,
    show: false,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    autoHideMenuBar: true,
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
      nodeIntegration: false,
      contextIsolation: true,
      experimentalFeatures: true,
    },
  })



  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
    win?.show() // Show window after content loads

    // Try to set rounded corners after window is shown
    try {
      // Inject CSS to force rounded corners on all platforms
      win?.webContents.insertCSS(`
        html, body, #root, .app {
          border-radius: 20px !important;
          overflow: hidden !important;
          -webkit-clip-path: inset(0 round 20px) !important;
          clip-path: inset(0 round 20px) !important;
        }
      `)
    } catch (e) {
      console.log('Could not inject CSS for rounded corners:', e)
    }
  })



  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }

  // Register global hotkey to toggle window visibility
  globalShortcut.register('Alt+F9', () => {
    if (win) {
      if (win.isVisible()) {
        // Fade out animation
        win.setOpacity(0)
        setTimeout(() => {
          win?.hide()
          win?.setOpacity(1)
        }, 150)
      } else {
        // Fade in animation
        win.setOpacity(0)
        win.show()
        win.focus()

        // Animate opacity from 0 to 1
        let opacity = 0
        const fadeIn = setInterval(() => {
          opacity += 0.1
          if (opacity >= 1) {
            opacity = 1
            clearInterval(fadeIn)
          }
          win?.setOpacity(opacity)
        }, 15)
      }
    }
  })
}

// IPC handlers for Roblox detection and anti-AFK
ipcMain.handle('get-roblox-instances', async () => {
  robloxInstances = await detectRobloxInstances()
  return robloxInstances
})

ipcMain.handle('start-anti-afk', async (event, instanceId: string) => {
  const instance = robloxInstances.find(inst => inst.id === instanceId)
  if (instance) {
    startAntiAfk(instanceId, instance.pid)
    instance.isActive = true
    return { success: true, message: `Started anti-AFK for ${instance.name}` }
  }
  return { success: false, message: 'Instance not found' }
})

ipcMain.handle('stop-anti-afk', async (event, instanceId: string) => {
  const instance = robloxInstances.find(inst => inst.id === instanceId)
  if (instance) {
    stopAntiAfk(instanceId)
    instance.isActive = false
    return { success: true, message: `Stopped anti-AFK for ${instance.name}` }
  }
  return { success: false, message: 'Instance not found' }
})

ipcMain.handle('toggle-anti-afk', async (event, instanceId: string) => {
  const instance = robloxInstances.find(inst => inst.id === instanceId)
  if (instance) {
    if (instance.isActive) {
      stopAntiAfk(instanceId)
      instance.isActive = false
      return { success: true, message: `Stopped anti-AFK for ${instance.name}`, isActive: false }
    } else {
      startAntiAfk(instanceId, instance.pid)
      instance.isActive = true
      return { success: true, message: `Started anti-AFK for ${instance.name}`, isActive: true }
    }
  }
  return { success: false, message: 'Instance not found', isActive: false }
})

// Auto-refresh Roblox instances every 10 seconds
setInterval(async () => {
  const newInstances = await detectRobloxInstances()

  // Update existing instances and preserve active states
  const updatedInstances = newInstances.map(newInst => {
    const existing = robloxInstances.find(inst => inst.pid === newInst.pid)
    if (existing) {
      return { ...newInst, isActive: existing.isActive }
    }
    return newInst
  })

  // Clean up intervals for instances that no longer exist
  const currentPids = new Set(updatedInstances.map(inst => inst.pid))
  for (const [instanceId, interval] of antiAfkIntervals.entries()) {
    const pid = parseInt(instanceId.replace('roblox_', ''))
    if (!currentPids.has(pid)) {
      clearInterval(interval)
      antiAfkIntervals.delete(instanceId)
      console.log(`Cleaned up anti-AFK for removed instance ${instanceId}`)
    }
  }

  robloxInstances = updatedInstances

  // Send updated instances to renderer
  if (win && !win.isDestroyed()) {
    win.webContents.send('roblox-instances-updated', robloxInstances)
  }
}, 10000) // 10 seconds

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    globalShortcut.unregisterAll()
    app.quit()
    win = null
  }
})

app.on('will-quit', () => {
  // Unregister all shortcuts.
  globalShortcut.unregisterAll()
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.whenReady().then(createWindow)
