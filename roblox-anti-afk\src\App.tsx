import { useState, useEffect } from 'react'
import { GlassCard, GlassButton, StatusDot } from './components'
import './App.css'

interface RobloxInstance {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'active';
  pid: number;
  antiAfkActive: boolean;
  uptime: string;
}

function App() {
  const [instances, setInstances] = useState<RobloxInstance[]>([
    {
      id: '1',
      name: 'Roblox Player',
      status: 'online',
      pid: 12345,
      antiAfkActive: false,
      uptime: '2h 34m'
    },
    {
      id: '2',
      name: 'Roblox Player',
      status: 'online',
      pid: 67891,
      antiAfkActive: false,
      uptime: '45m'
    }
  ]);

  const [globalAntiAfk, setGlobalAntiAfk] = useState(false);

  const toggleAntiAfk = (instanceId: string) => {
    setInstances(prev => prev.map(instance =>
      instance.id === instanceId
        ? {
            ...instance,
            antiAfkActive: !instance.antiAfkActive,
            status: !instance.antiAfkActive ? 'active' : 'online'
          }
        : instance
    ));
  };

  const toggleGlobalAntiAfk = () => {
    const newState = !globalAntiAfk;
    setGlobalAntiAfk(newState);
    setInstances(prev => prev.map(instance => ({
      ...instance,
      antiAfkActive: instance.status !== 'offline' ? newState : false,
      status: instance.status !== 'offline' ? (newState ? 'active' : 'online') : 'offline'
    })));
  };



  const activeCount = instances.filter(i => i.antiAfkActive).length;
  const onlineCount = instances.filter(i => i.status !== 'offline').length;

  return (
    <div className="app">
      <div className="app-header">
        <div className="header-content">
          <div className="title-section">
            <h1 className="app-title">Anti-AFK</h1>
            <div className="app-stats">
              <span className="stat">
                <span className="stat-value">{onlineCount}</span>
                <span className="stat-label">Online</span>
              </span>
              <span className="stat-divider">•</span>
              <span className="stat">
                <span className="stat-value">{activeCount}</span>
                <span className="stat-label">Active</span>
              </span>
            </div>
          </div>
          <div className="header-actions">
            <GlassButton
              onClick={toggleGlobalAntiAfk}
              variant={globalAntiAfk ? "danger" : "primary"}
              className="master-toggle"
            >
              {globalAntiAfk ? "Stop All" : "Start All"}
            </GlassButton>
          </div>
        </div>
      </div>

      <div className="app-content">
        <GlassCard className="instances-container">
          <div className="instances-header">
            <h2>Instances</h2>
            <div className="instances-count">{instances.length} detected</div>
          </div>

          <div className="instances-grid">
            {instances.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">○</div>
                <div className="empty-title">No instances found</div>
                <div className="empty-subtitle">Launch Roblox to get started</div>
              </div>
            ) : (
              instances.map(instance => (
                <div key={instance.id} className="instance-card">
                  <div className="instance-header">
                    <div className="instance-info">
                      <StatusDot status={instance.status} />
                      <div className="instance-details">
                        <div className="instance-name">{instance.name}</div>
                        <div className="instance-meta">
                          PID {instance.pid} • {instance.uptime}
                        </div>
                      </div>
                    </div>
                    <div className="instance-status">
                      <span className={`status-badge ${instance.status}`}>
                        {instance.status === 'active' ? 'Protected' :
                         instance.status === 'online' ? 'Ready' : 'Offline'}
                      </span>
                    </div>
                  </div>

                  <div className="instance-actions">
                    <GlassButton
                      onClick={() => toggleAntiAfk(instance.id)}
                      variant={instance.antiAfkActive ? "danger" : "success"}
                      disabled={instance.status === 'offline'}
                      className="instance-toggle"
                    >
                      {instance.antiAfkActive ? "Disable" : "Enable"}
                    </GlassButton>
                  </div>
                </div>
              ))
            )}
          </div>
        </GlassCard>
      </div>
    </div>
  )
}

export default App
