import { useState, useEffect } from 'react'
import { GlassCard, GlassButton, StatusDot } from './components'
import './App.css'

interface RobloxInstance {
  id: string;
  name: string;
  isOn: boolean;
  pid: number;
}

function App() {
  const [instances, setInstances] = useState<RobloxInstance[]>([
    {
      id: '1',
      name: 'Roblox Player',
      isOn: false,
      pid: 12345
    },
    {
      id: '2',
      name: 'Roblox Player',
      isOn: false,
      pid: 67891
    }
  ]);

  const [allOn, setAllOn] = useState(false);

  const toggle = (instanceId: string) => {
    setInstances(prev => prev.map(instance =>
      instance.id === instanceId
        ? { ...instance, isOn: !instance.isOn }
        : instance
    ));
  };

  const toggleAll = () => {
    const newState = !allOn;
    setAllOn(newState);
    setInstances(prev => prev.map(instance => ({
      ...instance,
      isOn: newState
    })));
  };



  const onCount = instances.filter(i => i.isOn).length;
  const totalCount = instances.length;

  return (
    <div className="app">
      <div className="app-header">
        <div className="header-content">
          <div className="title-section">
            <h1 className="app-title">Anti-AFK</h1>
            <div className="app-stats">
              <span className="stat">
                <span className="stat-value">{onCount}</span>
                <span className="stat-label">ON</span>
              </span>
              <span className="stat-divider">•</span>
              <span className="stat">
                <span className="stat-value">{totalCount}</span>
                <span className="stat-label">TOTAL</span>
              </span>
            </div>
          </div>
          <div className="header-actions">
            <GlassButton
              onClick={toggleAll}
              variant={allOn ? "danger" : "primary"}
              className="master-toggle"
            >
              {allOn ? "Turn Off All" : "Turn On All"}
            </GlassButton>
          </div>
        </div>
      </div>

      <div className="app-content">
        <GlassCard className="instances-container">
          <div className="instances-header">
            <h2>Instances</h2>
            <div className="instances-count">{instances.length} detected</div>
          </div>

          <div className="instances-grid">
            {instances.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">○</div>
                <div className="empty-title">No instances found</div>
                <div className="empty-subtitle">Launch Roblox to get started</div>
              </div>
            ) : (
              instances.map(instance => (
                <div key={instance.id} className="instance-card">
                  <div className="instance-header">
                    <div className="instance-info">
                      <StatusDot status={instance.isOn ? 'active' : 'offline'} />
                      <div className="instance-details">
                        <div className="instance-name">{instance.name}</div>
                        <div className="instance-meta">PID {instance.pid}</div>
                      </div>
                    </div>
                    <div className="instance-status">
                      <span className={`status-badge ${instance.isOn ? 'on' : 'off'}`}>
                        {instance.isOn ? 'Anti-AFK ON' : 'OFF'}
                      </span>
                    </div>
                  </div>

                  <div className="instance-actions">
                    <GlassButton
                      onClick={() => toggle(instance.id)}
                      variant={instance.isOn ? "danger" : "success"}
                      className="instance-toggle"
                    >
                      {instance.isOn ? "Turn Off" : "Turn On"}
                    </GlassButton>
                  </div>
                </div>
              ))
            )}
          </div>
        </GlassCard>
      </div>
    </div>
  )
}

export default App
