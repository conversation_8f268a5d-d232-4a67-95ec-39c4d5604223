import { useState, useEffect } from 'react'
import './App.css'

interface RobloxInstance {
  id: string;
  name: string;
  pid: number;
  windowTitle: string;
  isActive: boolean;
}

function App() {
  const [instances, setInstances] = useState<RobloxInstance[]>([]);
  const [selectedPid, setSelectedPid] = useState<number | null>(null);
  const [isActive, setIsActive] = useState(false);

  // Load real instances
  useEffect(() => {
    const loadInstances = async () => {
      try {
        if ((window as any).electronAPI?.getRobloxInstances) {
          const realInstances = await (window as any).electronAPI.getRobloxInstances();
          if (realInstances && realInstances.length > 0) {
            setInstances(realInstances);
            if (!selectedPid && realInstances.length > 0) {
              setSelectedPid(realInstances[0].pid);
            }
          }
        }
      } catch (error) {
        console.error('Error loading instances:', error);
      }
    };
    loadInstances();
    const interval = setInterval(loadInstances, 5000);
    return () => clearInterval(interval);
  }, []);

  const toggleAntiAfk = async () => {
    if (!selectedPid) return;

    const instanceId = `roblox_${selectedPid}`;

    try {
      if ((window as any).electronAPI?.toggleAntiAfk) {
        const result = await (window as any).electronAPI.toggleAntiAfk(instanceId);
        if (result.success) {
          setIsActive(result.isActive);
          console.log(result.message);
        }
      }
    } catch (error) {
      console.error('Error toggling anti-AFK:', error);
    }
  };

  return (
    <div className="simple-app">
      <div className="simple-card">
        <h2>Anti-AFK</h2>

        <div className="pid-selector">
          <label>Select Roblox PID:</label>
          <select
            value={selectedPid || ''}
            onChange={(e) => setSelectedPid(Number(e.target.value))}
          >
            <option value="">Choose PID</option>
            {instances.map(instance => (
              <option key={instance.pid} value={instance.pid}>
                PID {instance.pid} - {instance.windowTitle}
              </option>
            ))}
          </select>
        </div>

        <div className="toggle-section">
          <button
            className={`big-toggle ${isActive ? 'on' : 'off'}`}
            onClick={toggleAntiAfk}
            disabled={!selectedPid}
          >
            {isActive ? 'ON' : 'OFF'}
          </button>
        </div>

        <div className="status">
          Status: {isActive ? 'ACTIVE (10s intervals)' : 'INACTIVE'}
        </div>
      </div>
    </div>
  );
}

export default App;
