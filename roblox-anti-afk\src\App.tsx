import { useState } from 'react'
import { <PERSON><PERSON>ard, GlassButton, StatusDot } from './components'
import './App.css'

// Mock data for Roblox instances
interface RobloxInstance {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'active';
  pid: number;
  antiAfkActive: boolean;
}

function App() {
  const [instances, setInstances] = useState<RobloxInstance[]>([
    {
      id: '1',
      name: 'Roblox Player',
      status: 'online',
      pid: 12345,
      antiAfkActive: false
    },
    {
      id: '2',
      name: 'Roblox Studio',
      status: 'offline',
      pid: 67890,
      antiAfkActive: false
    }
  ]);

  const [globalAntiAfk, setGlobalAntiAfk] = useState(false);

  const toggleAntiAfk = (instanceId: string) => {
    setInstances(prev => prev.map(instance =>
      instance.id === instanceId
        ? { ...instance, antiAfkActive: !instance.antiAfkActive, status: !instance.antiAfkActive ? 'active' : 'online' }
        : instance
    ));
  };

  const toggleGlobalAntiAfk = () => {
    const newState = !globalAntiAfk;
    setGlobalAntiAfk(newState);
    setInstances(prev => prev.map(instance => ({
      ...instance,
      antiAfkActive: instance.status !== 'offline' ? newState : false,
      status: instance.status !== 'offline' ? (newState ? 'active' : 'online') : 'offline'
    })));
  };

  const refreshInstances = () => {
    // In a real app, this would scan for Roblox processes
    console.log('Refreshing Roblox instances...');
  };

  return (
    <div className="app-container">
      <header className="app-header">
        <GlassCard className="header-card">
          <h1 className="app-title">Roblox Anti-AFK</h1>
          <p className="app-subtitle">Keep your Roblox sessions active</p>
        </GlassCard>
      </header>

      <main className="app-main">
        <div className="controls-section">
          <GlassCard className="controls-card">
            <div className="controls-header">
              <h2>Global Controls</h2>
              <GlassButton onClick={refreshInstances} variant="primary">
                🔄 Refresh Instances
              </GlassButton>
            </div>
            <div className="global-control">
              <GlassButton
                onClick={toggleGlobalAntiAfk}
                variant={globalAntiAfk ? "danger" : "success"}
                className="global-toggle"
              >
                {globalAntiAfk ? "🛑 Stop All Anti-AFK" : "▶️ Start All Anti-AFK"}
              </GlassButton>
            </div>
          </GlassCard>
        </div>

        <div className="instances-section">
          <GlassCard className="instances-card">
            <h2 className="instances-title">Detected Instances</h2>
            <div className="instances-list">
              {instances.length === 0 ? (
                <div className="no-instances">
                  <p>No Roblox instances detected</p>
                  <p className="hint">Launch Roblox and click "Refresh Instances"</p>
                </div>
              ) : (
                instances.map(instance => (
                  <GlassCard key={instance.id} className="instance-item" hover={false}>
                    <div className="instance-info">
                      <div className="instance-header">
                        <StatusDot status={instance.status} />
                        <span className="instance-name">{instance.name}</span>
                        <span className="instance-pid">PID: {instance.pid}</span>
                      </div>
                      <div className="instance-status">
                        Status: <span className={`status-text ${instance.status}`}>
                          {instance.status === 'active' ? 'Anti-AFK Active' :
                           instance.status === 'online' ? 'Online' : 'Offline'}
                        </span>
                      </div>
                    </div>
                    <div className="instance-controls">
                      <GlassButton
                        onClick={() => toggleAntiAfk(instance.id)}
                        variant={instance.antiAfkActive ? "danger" : "success"}
                        disabled={instance.status === 'offline'}
                      >
                        {instance.antiAfkActive ? "Stop" : "Start"}
                      </GlassButton>
                    </div>
                  </GlassCard>
                ))
              )}
            </div>
          </GlassCard>
        </div>
      </main>
    </div>
  )
}

export default App
