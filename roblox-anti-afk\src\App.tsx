import { useState, useEffect } from 'react'
import { GlassCard, GlassButton, StatusDot } from './components'
import './App.css'

interface RobloxInstance {
  id: string;
  name: string;
  isOn: boolean;
  pid: number;
}

function App() {
  const [instances, setInstances] = useState<RobloxInstance[]>([
    {
      id: '1',
      name: 'Roblox Player',
      isOn: false,
      pid: 12345
    },
    {
      id: '2',
      name: 'Roblox Player',
      isOn: true,
      pid: 67891
    },
    {
      id: '3',
      name: '<PERSON>lox Player',
      isOn: false,
      pid: 54321
    },
    {
      id: '4',
      name: 'Roblox Player',
      isOn: false,
      pid: 98765
    },
    {
      id: '5',
      name: 'Roblox Player',
      isOn: true,
      pid: 11111
    }
  ]);

  const [selectedInstance, setSelectedInstance] = useState<string>('');
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Dynamic window resizing based on dropdown state
  useEffect(() => {
    const baseHeight = 320
    const itemHeight = 60 // Height per dropdown item
    const maxVisibleItems = 4 // Max items before scrolling
    const visibleItems = Math.min(instances.length, maxVisibleItems)
    const dynamicHeight = dropdownOpen
      ? baseHeight + (visibleItems * itemHeight) + 20 // Extra padding
      : baseHeight

    // Send resize message to Electron (if available)
    if ((window as any).electronAPI?.resizeWindow) {
      (window as any).electronAPI.resizeWindow(420, dynamicHeight)
    }
  }, [dropdownOpen, instances.length])

  const toggle = (instanceId: string) => {
    setInstances(prev => prev.map(instance =>
      instance.id === instanceId
        ? { ...instance, isOn: !instance.isOn }
        : instance
    ));
  };



  const onCount = instances.filter(i => i.isOn).length;
  const totalCount = instances.length;

  return (
    <div className="app">
      <div className="app-header">
        <div className="header-content">
          <div className="title-section">
            <img src="/sentinel-logo.png" alt="Sentinel" className="app-logo" />
            <div className="app-stats">
              <span className="stat">
                <span className="stat-value">{onCount}</span>
                <span className="stat-label">ON</span>
              </span>
              <span className="stat-divider">•</span>
              <span className="stat">
                <span className="stat-value">{totalCount}</span>
                <span className="stat-label">TOTAL</span>
              </span>
            </div>
          </div>

        </div>
      </div>

      <GlassCard className="control-panel">
        <div className="control-section">
          <label className="control-label">Select Roblox Instance</label>
          <div className="dropdown-container">
            <button
              className="dropdown-trigger"
              onClick={() => setDropdownOpen(!dropdownOpen)}
            >
              <div className="dropdown-content">
                {selectedInstance ? (
                  <div className="selected-instance">
                    <StatusDot status={instances.find(i => i.id === selectedInstance)?.isOn ? 'active' : 'offline'} />
                    <span>{instances.find(i => i.id === selectedInstance)?.name} (PID {instances.find(i => i.id === selectedInstance)?.pid})</span>
                  </div>
                ) : (
                  <span className="placeholder">Choose an instance...</span>
                )}
              </div>
              <div className={`dropdown-arrow ${dropdownOpen ? 'open' : ''}`}>
                <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                  <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </button>

            {dropdownOpen && (
              <div className="dropdown-menu">
                {instances.map(instance => (
                  <button
                    key={instance.id}
                    className={`dropdown-item ${selectedInstance === instance.id ? 'selected' : ''}`}
                    onClick={() => {
                      setSelectedInstance(instance.id);
                      setDropdownOpen(false);
                    }}
                  >
                    <StatusDot status={instance.isOn ? 'active' : 'offline'} />
                    <div className="dropdown-item-info">
                      <span className="dropdown-item-name">{instance.name}</span>
                      <span className="dropdown-item-meta">PID {instance.pid}</span>
                    </div>
                    <span className={`dropdown-item-status ${instance.isOn ? 'on' : 'off'}`}>
                      {instance.isOn ? 'ON' : 'OFF'}
                    </span>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {selectedInstance && (
          <div className="action-section">
            <GlassButton
              onClick={() => toggle(selectedInstance)}
              variant={instances.find(i => i.id === selectedInstance)?.isOn ? "danger" : "success"}
              className={`main-action-button ${instances.find(i => i.id === selectedInstance)?.isOn ? 'on' : ''}`}
            >
              {instances.find(i => i.id === selectedInstance)?.isOn ? "Turn Off" : "Turn On"}
            </GlassButton>
          </div>
        )}
      </GlassCard>
    </div>
  )
}

export default App
