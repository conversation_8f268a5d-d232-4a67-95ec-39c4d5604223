import { useState, useEffect } from 'react'
import { GlassCard, GlassButton, StatusDot } from './components'
import './App.css'

interface RobloxInstance {
  id: string;
  name: string;
  pid: number;
  windowTitle: string;
  isActive: boolean;
}

function App() {
  const [instances, setInstances] = useState<RobloxInstance[]>([
    {
      id: 'roblox_12345',
      name: 'Roblox Player',
      pid: 12345,
      windowTitle: 'Roblox Player (PID 12345)',
      isActive: false
    },
    {
      id: 'roblox_67891',
      name: 'Roblox Player',
      pid: 67891,
      windowTitle: 'Roblox Player (PID 67891)',
      isActive: true
    },
    {
      id: 'roblox_54321',
      name: 'Roblox Player',
      pid: 54321,
      windowTitle: 'Roblox Player (PID 54321)',
      isActive: false
    },
    {
      id: 'roblox_98765',
      name: '<PERSON>lox Player',
      pid: 98765,
      windowTitle: 'Roblox Player (PID 98765)',
      isActive: false
    },
    {
      id: 'roblox_11111',
      name: 'Roblox Player',
      pid: 11111,
      windowTitle: 'Roblox Player (PID 11111)',
      isActive: true
    }
  ]);
  const [selectedInstance, setSelectedInstance] = useState<string>('');
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Try to load real instances but fallback to dummy data
  useEffect(() => {
    const loadInstances = async () => {
      console.log('=== TESTING ELECTRON API ===');
      console.log('window.electronAPI available:', !!(window as any).electronAPI);

      if ((window as any).electronAPI) {
        console.log('electronAPI methods:', Object.keys((window as any).electronAPI));

        // Test basic function
        if ((window as any).electronAPI.test) {
          const testResult = (window as any).electronAPI.test();
          console.log('Test result:', testResult);
        }
      }

      console.log('Attempting to load real Roblox instances...');
      try {
        if ((window as any).electronAPI?.getRobloxInstances) {
          console.log('Calling getRobloxInstances...');
          const robloxInstances = await (window as any).electronAPI.getRobloxInstances();
          console.log('getRobloxInstances returned:', robloxInstances);

          if (robloxInstances && robloxInstances.length > 0) {
            console.log('Loaded real instances:', robloxInstances);
            setInstances(robloxInstances);
          } else {
            console.log('No real instances found, keeping dummy data');
          }
        } else {
          console.log('electronAPI.getRobloxInstances not available, using dummy data');
        }
      } catch (error) {
        console.error('Error loading real instances, using dummy data:', error);
      }
    };

    // Try to load real instances after a short delay
    setTimeout(loadInstances, 1000);
  }, []);

  // Listen for instance updates
  useEffect(() => {
    if ((window as any).electronAPI?.onRobloxInstancesUpdated) {
      const cleanup = (window as any).electronAPI.onRobloxInstancesUpdated((updatedInstances: RobloxInstance[]) => {
        setInstances(updatedInstances);

        // Update selected instance if it no longer exists
        if (selectedInstance && !updatedInstances.find(inst => inst.id === selectedInstance)) {
          setSelectedInstance('');
        }
      });

      return cleanup;
    }
  }, [selectedInstance]);



  // Click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const dropdown = document.querySelector('.dropdown-container');
      if (dropdown && !dropdown.contains(e.target as Node)) {
        setDropdownOpen(false);
      }
    };

    if (dropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [dropdownOpen]);



  const toggle = async (instanceId: string) => {
    console.log('Toggling anti-AFK for instance:', instanceId);

    // First update local state immediately for responsiveness
    setInstances(prev => prev.map(instance =>
      instance.id === instanceId
        ? { ...instance, isActive: !instance.isActive }
        : instance
    ));

    // Try to call backend if available
    try {
      if ((window as any).electronAPI?.toggleAntiAfk) {
        const result = await (window as any).electronAPI.toggleAntiAfk(instanceId);
        console.log('Backend toggle result:', result);

        if (result.success) {
          // Update with backend result
          setInstances(prev => prev.map(instance =>
            instance.id === instanceId
              ? { ...instance, isActive: result.isActive }
              : instance
          ));
        }
      } else {
        console.log('Backend not available, using local toggle only');
      }
    } catch (error) {
      console.error('Error calling backend toggle:', error);
      // Local state already updated, so this is fine
    }
  };



  const onCount = instances.filter(i => i.isActive).length;
  const totalCount = instances.length;

  return (
    <div className="app">
      <div className="app-header">
        <div className="header-content">
          <div className="title-section">
            <img src="/sentinel-logo.png" alt="Sentinel" className="app-logo" />
            <div className="app-stats">
              <span className="stat">
                <span className="stat-value">{onCount}</span>
                <span className="stat-label">ON</span>
              </span>
              <span className="stat-divider">•</span>
              <span className="stat">
                <span className="stat-value">{totalCount}</span>
                <span className="stat-label">TOTAL</span>
              </span>
            </div>
          </div>

        </div>
      </div>

      <GlassCard className="control-panel">
        <div className="control-section">
          <label className="control-label">Select Roblox Instance</label>
          <div className="dropdown-container">
            <button
              className="dropdown-trigger"
              onClick={() => setDropdownOpen(!dropdownOpen)}
              disabled={loading}
            >
              <div className="dropdown-content">
                {loading ? (
                  <span className="placeholder">Detecting Roblox instances...</span>
                ) : selectedInstance ? (
                  <div className="selected-instance">
                    <StatusDot status={instances.find(i => i.id === selectedInstance)?.isActive ? 'active' : 'offline'} />
                    <span>{instances.find(i => i.id === selectedInstance)?.windowTitle || `${instances.find(i => i.id === selectedInstance)?.name} (PID ${instances.find(i => i.id === selectedInstance)?.pid})`}</span>
                  </div>
                ) : instances.length === 0 ? (
                  <span className="placeholder">No Roblox instances found</span>
                ) : (
                  <span className="placeholder">Choose an instance...</span>
                )}
              </div>
              <div className={`dropdown-arrow ${dropdownOpen ? 'open' : ''}`}>
                <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                  <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </button>

            {dropdownOpen && !loading && (
              <div className="dropdown-menu">
                {instances.length === 0 ? (
                  <div className="dropdown-item disabled">
                    <span>No Roblox instances found</span>
                  </div>
                ) : (
                  instances.map(instance => (
                    <button
                      key={instance.id}
                      className={`dropdown-item ${selectedInstance === instance.id ? 'selected' : ''}`}
                      onClick={() => {
                        setSelectedInstance(instance.id);
                        setDropdownOpen(false);
                      }}
                    >
                      <StatusDot status={instance.isActive ? 'active' : 'offline'} />
                      <div className="dropdown-item-info">
                        <span className="dropdown-item-name">{instance.name}</span>
                        <span className="dropdown-item-meta">PID {instance.pid}</span>
                      </div>
                      <span className={`dropdown-item-status ${instance.isActive ? 'on' : 'off'}`}>
                        {instance.isActive ? 'ON' : 'OFF'}
                      </span>
                    </button>
                  ))
                )}
              </div>
            )}
          </div>
        </div>

        {selectedInstance && !loading && (
          <div className="action-section">
            <GlassButton
              onClick={() => toggle(selectedInstance)}
              variant="default"
              className={`main-action-button ${instances.find(i => i.id === selectedInstance)?.isActive ? 'on' : 'off'}`}
            >
              {instances.find(i => i.id === selectedInstance)?.isActive ? "ON" : "OFF"}
            </GlassButton>
          </div>
        )}
      </GlassCard>
    </div>
  )
}

export default App
