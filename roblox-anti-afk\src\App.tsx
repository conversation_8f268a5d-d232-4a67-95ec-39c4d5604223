import { useState, useEffect } from 'react'
import './App.css'

interface RobloxInstance {
  id: string;
  name: string;
  pid: number;
  windowTitle: string;
  isActive: boolean;
}

function App() {
  const [instances, setInstances] = useState<RobloxInstance[]>([
    {
      id: 'roblox_12345',
      name: 'Roblox Player',
      pid: 12345,
      windowTitle: 'Roblox Player (PID 12345)',
      isActive: false
    },
    {
      id: 'roblox_67891',
      name: 'Roblox Player',
      pid: 67891,
      windowTitle: 'Roblox Player (PID 67891)',
      isActive: true
    },
    {
      id: 'roblox_54321',
      name: 'Roblox Player',
      pid: 54321,
      windowTitle: 'Roblox Player (PID 54321)',
      isActive: false
    }
  ]);
  const [selectedInstance, setSelectedInstance] = useState<string>('');
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Try to load real instances
  useEffect(() => {
    const loadInstances = async () => {
      console.log('Loading instances...');
      try {
        if ((window as any).electronAPI?.getRobloxInstances) {
          const realInstances = await (window as any).electronAPI.getRobloxInstances();
          if (realInstances && realInstances.length > 0) {
            setInstances(realInstances);
          }
        }
      } catch (error) {
        console.error('Error loading instances:', error);
      }
    };
    setTimeout(loadInstances, 1000);
  }, []);

  const toggle = (instanceId: string) => {
    setInstances(prev => prev.map(instance =>
      instance.id === instanceId
        ? { ...instance, isActive: !instance.isActive }
        : instance
    ));
  };

  return (
    <div className="app">
      <div className="glass-card">
        <div className="header">
          <div className="logo">🛡️</div>
          <h1>Sentinel</h1>
        </div>

        <div className="dropdown-container">
          <div
            className="dropdown-trigger"
            onClick={() => setDropdownOpen(!dropdownOpen)}
          >
            <span>
              {selectedInstance
                ? instances.find(i => i.id === selectedInstance)?.windowTitle || 'Select Instance'
                : 'Select Roblox Instance'
              }
            </span>
            <span className="dropdown-arrow">▼</span>
          </div>

          {dropdownOpen && (
            <div className="dropdown-menu">
              {instances.map(instance => (
                <div
                  key={instance.id}
                  className="dropdown-item"
                  onClick={() => {
                    setSelectedInstance(instance.id);
                    setDropdownOpen(false);
                  }}
                >
                  <div className="instance-info">
                    <span className="instance-title">{instance.windowTitle}</span>
                    <span className="instance-pid">PID: {instance.pid}</span>
                  </div>
                  <div className={`status-dot ${instance.isActive ? 'active' : 'inactive'}`}></div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="instances-list">
          {instances.slice(0, 3).map(instance => (
            <div key={instance.id} className="instance-item">
              <div className="instance-details">
                <div className="instance-name">{instance.windowTitle}</div>
                <div className="instance-pid">PID: {instance.pid}</div>
              </div>
              <div className="instance-controls">
                <div className={`status-dot ${instance.isActive ? 'active' : 'inactive'}`}></div>
                <button
                  className="toggle-button"
                  onClick={() => toggle(instance.id)}
                >
                  {instance.isActive ? 'ON' : 'OFF'}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default App;
