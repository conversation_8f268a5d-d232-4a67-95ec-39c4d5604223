import { ipcMain, app, globalShortcut, BrowserWindow } from "electron";
import { createRequire } from "node:module";
import { fileURLToPath } from "node:url";
import path from "node:path";
import { exec } from "node:child_process";
import { promisify } from "node:util";
const execAsync = promisify(exec);
createRequire(import.meta.url);
let robloxInstances = [];
let antiAfkIntervals = /* @__PURE__ */ new Map();
let lastActiveWindow = null;
async function detectRobloxInstances() {
  console.log("Detecting Roblox instances...");
  try {
    if (process.platform === "win32") {
      const { stdout } = await execAsync('tasklist /fi "imagename eq RobloxPlayerBeta.exe" /fo csv');
      console.log("Tasklist output:", stdout);
      const lines = stdout.split("\n").slice(1);
      const instances = [];
      for (const line of lines) {
        if (line.trim() && !line.includes("INFO: No tasks")) {
          const parts = line.split('","').map((part) => part.replace(/"/g, ""));
          console.log("Processing line parts:", parts);
          if (parts.length >= 2) {
            const pid = parseInt(parts[1]);
            if (!isNaN(pid)) {
              console.log(`Found Roblox instance with PID: ${pid}`);
              let windowTitle = `Roblox Player (PID ${pid})`;
              try {
                const { stdout: titleOutput } = await execAsync(`powershell "Get-Process -Id ${pid} | Select-Object MainWindowTitle | Format-Table -HideTableHeaders"`);
                const title = titleOutput.trim();
                if (title && title !== "" && !title.includes("MainWindowTitle")) {
                  windowTitle = title;
                }
              } catch (e) {
                console.log(`Could not get window title for PID ${pid}:`, e);
              }
              instances.push({
                id: `roblox_${pid}`,
                name: "Roblox Player",
                pid,
                windowTitle,
                isActive: antiAfkIntervals.has(`roblox_${pid}`)
              });
            }
          }
        }
      }
      console.log(`Found ${instances.length} Roblox instances:`, instances);
      return instances;
    } else {
      const { stdout } = await execAsync("ps aux | grep -i roblox | grep -v grep");
      const lines = stdout.split("\n").filter((line) => line.trim());
      return lines.map((line, index) => {
        const parts = line.trim().split(/\s+/);
        const pid = parseInt(parts[1]);
        return {
          id: `roblox_${pid}`,
          name: "Roblox Player",
          pid,
          windowTitle: `Roblox Player (PID ${pid})`,
          isActive: antiAfkIntervals.has(`roblox_${pid}`)
        };
      });
    }
  } catch (error) {
    console.log("Error detecting Roblox instances:", error);
    return [
      {
        id: "roblox_12345",
        name: "Roblox Player",
        pid: 12345,
        windowTitle: "Roblox Player (PID 12345)",
        isActive: false
      },
      {
        id: "roblox_67891",
        name: "Roblox Player",
        pid: 67891,
        windowTitle: "Roblox Player (PID 67891)",
        isActive: false
      },
      {
        id: "roblox_54321",
        name: "Roblox Player",
        pid: 54321,
        windowTitle: "Roblox Player (PID 54321)",
        isActive: false
      }
    ];
  }
}
async function getCurrentActiveWindow() {
  try {
    if (process.platform === "win32") {
      const { stdout } = await execAsync('powershell "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Application]::OpenForms | Select-Object Text"');
      return stdout.trim() || null;
    }
    return null;
  } catch (error) {
    return null;
  }
}
async function focusRobloxWindow(pid) {
  try {
    if (process.platform === "win32") {
      lastActiveWindow = await getCurrentActiveWindow();
      await execAsync(`powershell "Add-Type -AssemblyName Microsoft.VisualBasic; Add-Type -AssemblyName System.Windows.Forms; $p = Get-Process -Id ${pid}; if ($p.MainWindowHandle -ne 0) { [Microsoft.VisualBasic.Interaction]::AppActivate($p.Id) }"`);
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("Error focusing Roblox window:", error);
    return false;
  }
}
async function sendJumpCommand() {
  try {
    if (process.platform === "win32") {
      await execAsync(`powershell "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(' ')"`);
    }
  } catch (error) {
    console.log("Error sending jump command:", error);
  }
}
async function restorePreviousWindow() {
  try {
    if (process.platform === "win32" && lastActiveWindow) {
      await execAsync(`powershell "Get-Process | Where-Object {$_.MainWindowTitle -like '*${lastActiveWindow}*'} | ForEach-Object { [Microsoft.VisualBasic.Interaction]::AppActivate($_.Id) }"`);
    }
  } catch (error) {
    console.log("Error restoring previous window:", error);
  }
}
async function performAntiAfk(instanceId, pid) {
  console.log(`Performing anti-AFK for instance ${instanceId} (PID: ${pid})`);
  const focused = await focusRobloxWindow(pid);
  if (!focused) {
    console.log("Failed to focus Roblox window");
    return;
  }
  await new Promise((resolve) => setTimeout(resolve, 500));
  await sendJumpCommand();
  await new Promise((resolve) => setTimeout(resolve, 200));
  await restorePreviousWindow();
  console.log(`Anti-AFK completed for instance ${instanceId}`);
}
function startAntiAfk(instanceId, pid) {
  if (antiAfkIntervals.has(instanceId)) {
    clearInterval(antiAfkIntervals.get(instanceId));
  }
  const interval = setInterval(() => {
    performAntiAfk(instanceId, pid);
  }, 8 * 60 * 1e3);
  antiAfkIntervals.set(instanceId, interval);
  console.log(`Started anti-AFK for instance ${instanceId} (PID: ${pid})`);
}
function stopAntiAfk(instanceId) {
  if (antiAfkIntervals.has(instanceId)) {
    clearInterval(antiAfkIntervals.get(instanceId));
    antiAfkIntervals.delete(instanceId);
    console.log(`Stopped anti-AFK for instance ${instanceId}`);
  }
}
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  console.log("=== CREATING WINDOW ===");
  win = new BrowserWindow({
    width: 420,
    height: 480,
    show: true,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    autoHideMenuBar: true,
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs"),
      nodeIntegration: false,
      contextIsolation: true,
      experimentalFeatures: true
    }
  });
  win.webContents.on("did-finish-load", async () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
    console.log("Window loaded, showing immediately...");
    win == null ? void 0 : win.show();
    win == null ? void 0 : win.focus();
    win == null ? void 0 : win.setAlwaysOnTop(true);
    try {
      win == null ? void 0 : win.webContents.insertCSS(`
        html, body, #root, .app {
          border-radius: 20px !important;
          overflow: hidden !important;
          -webkit-clip-path: inset(0 round 20px) !important;
          clip-path: inset(0 round 20px) !important;
        }
      `);
    } catch (e) {
      console.log("Could not inject CSS for rounded corners:", e);
    }
    console.log("Performing initial Roblox detection...");
    robloxInstances = await detectRobloxInstances();
    console.log("Initial instances found:", robloxInstances);
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
  globalShortcut.register("Alt+F9", () => {
    if (win) {
      if (win.isVisible()) {
        win.setOpacity(0);
        setTimeout(() => {
          win == null ? void 0 : win.hide();
          win == null ? void 0 : win.setOpacity(1);
        }, 150);
      } else {
        win.setOpacity(0);
        win.show();
        win.focus();
        let opacity = 0;
        const fadeIn = setInterval(() => {
          opacity += 0.1;
          if (opacity >= 1) {
            opacity = 1;
            clearInterval(fadeIn);
          }
          win == null ? void 0 : win.setOpacity(opacity);
        }, 15);
      }
    }
  });
}
ipcMain.handle("get-roblox-instances", async () => {
  console.log("=== IPC: get-roblox-instances called ===");
  try {
    robloxInstances = await detectRobloxInstances();
    console.log("IPC: detectRobloxInstances returned:", robloxInstances);
    console.log("IPC: returning instances count:", robloxInstances.length);
    return robloxInstances;
  } catch (error) {
    console.error("IPC: Error in get-roblox-instances:", error);
    const dummyInstances = [
      {
        id: "roblox_12345",
        name: "Roblox Player",
        pid: 12345,
        windowTitle: "Roblox Player (PID 12345)",
        isActive: false
      },
      {
        id: "roblox_67891",
        name: "Roblox Player",
        pid: 67891,
        windowTitle: "Roblox Player (PID 67891)",
        isActive: true
      }
    ];
    console.log("IPC: returning dummy instances:", dummyInstances);
    return dummyInstances;
  }
});
ipcMain.handle("start-anti-afk", async (event, instanceId) => {
  const instance = robloxInstances.find((inst) => inst.id === instanceId);
  if (instance) {
    startAntiAfk(instanceId, instance.pid);
    instance.isActive = true;
    return { success: true, message: `Started anti-AFK for ${instance.name}` };
  }
  return { success: false, message: "Instance not found" };
});
ipcMain.handle("stop-anti-afk", async (event, instanceId) => {
  const instance = robloxInstances.find((inst) => inst.id === instanceId);
  if (instance) {
    stopAntiAfk(instanceId);
    instance.isActive = false;
    return { success: true, message: `Stopped anti-AFK for ${instance.name}` };
  }
  return { success: false, message: "Instance not found" };
});
ipcMain.handle("toggle-anti-afk", async (event, instanceId) => {
  const instance = robloxInstances.find((inst) => inst.id === instanceId);
  if (instance) {
    if (instance.isActive) {
      stopAntiAfk(instanceId);
      instance.isActive = false;
      return { success: true, message: `Stopped anti-AFK for ${instance.name}`, isActive: false };
    } else {
      startAntiAfk(instanceId, instance.pid);
      instance.isActive = true;
      return { success: true, message: `Started anti-AFK for ${instance.name}`, isActive: true };
    }
  }
  return { success: false, message: "Instance not found", isActive: false };
});
setInterval(async () => {
  const newInstances = await detectRobloxInstances();
  const updatedInstances = newInstances.map((newInst) => {
    const existing = robloxInstances.find((inst) => inst.pid === newInst.pid);
    if (existing) {
      return { ...newInst, isActive: existing.isActive };
    }
    return newInst;
  });
  const currentPids = new Set(updatedInstances.map((inst) => inst.pid));
  for (const [instanceId, interval] of antiAfkIntervals.entries()) {
    const pid = parseInt(instanceId.replace("roblox_", ""));
    if (!currentPids.has(pid)) {
      clearInterval(interval);
      antiAfkIntervals.delete(instanceId);
      console.log(`Cleaned up anti-AFK for removed instance ${instanceId}`);
    }
  }
  robloxInstances = updatedInstances;
  if (win && !win.isDestroyed()) {
    win.webContents.send("roblox-instances-updated", robloxInstances);
  }
}, 1e4);
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    globalShortcut.unregisterAll();
    app.quit();
    win = null;
  }
});
app.on("will-quit", () => {
  globalShortcut.unregisterAll();
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
app.whenReady().then(createWindow);
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
