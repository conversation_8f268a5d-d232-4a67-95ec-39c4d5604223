import { app, globalShortcut, BrowserWindow, ipcMain } from "electron";
import { createRequire } from "node:module";
import { fileURLToPath } from "node:url";
import path from "node:path";
createRequire(import.meta.url);
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    width: 420,
    height: 320,
    show: false,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    autoHideMenuBar: true,
    roundedCorners: true,
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs"),
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
    win == null ? void 0 : win.show();
  });
  ipcMain.on("resize-window", (event, { width, height }) => {
    if (win) {
      const currentBounds = win.getBounds();
      const steps = 10;
      const widthStep = (width - currentBounds.width) / steps;
      const heightStep = (height - currentBounds.height) / steps;
      let currentStep = 0;
      const animate = () => {
        if (currentStep < steps) {
          currentStep++;
          const newWidth = Math.round(currentBounds.width + widthStep * currentStep);
          const newHeight = Math.round(currentBounds.height + heightStep * currentStep);
          win == null ? void 0 : win.setSize(newWidth, newHeight, false);
          setTimeout(animate, 16);
        }
      };
      animate();
    }
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
  globalShortcut.register("Alt+F9", () => {
    if (win) {
      if (win.isVisible()) {
        win.setOpacity(0);
        setTimeout(() => {
          win == null ? void 0 : win.hide();
          win == null ? void 0 : win.setOpacity(1);
        }, 150);
      } else {
        win.setOpacity(0);
        win.show();
        win.focus();
        let opacity = 0;
        const fadeIn = setInterval(() => {
          opacity += 0.1;
          if (opacity >= 1) {
            opacity = 1;
            clearInterval(fadeIn);
          }
          win == null ? void 0 : win.setOpacity(opacity);
        }, 15);
      }
    }
  });
}
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    globalShortcut.unregisterAll();
    app.quit();
    win = null;
  }
});
app.on("will-quit", () => {
  globalShortcut.unregisterAll();
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
app.whenReady().then(createWindow);
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
