:root {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Apple Liquid Glass Color Palette */
  --glass-bg-primary: rgba(255, 255, 255, 0.08);
  --glass-bg-secondary: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-shadow: rgba(0, 0, 0, 0.3);
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.7);
  --accent-blue: #007AFF;
  --accent-green: #34C759;
  --accent-red: #FF3B30;
  --background-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);

  color-scheme: dark;
  color: var(--text-primary);
  background: var(--background-gradient);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
  background: var(--background-gradient);
  background-attachment: fixed;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* Liquid Glass Components */
.glass-container {
  background: var(--glass-bg-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow:
    0 8px 32px var(--glass-shadow),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-container:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px var(--glass-shadow),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.glass-button {
  background: var(--glass-bg-secondary);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  font-family: inherit;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.glass-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.glass-button.primary {
  background: linear-gradient(135deg, var(--accent-blue), rgba(0, 122, 255, 0.8));
  border-color: var(--accent-blue);
}

.glass-button.success {
  background: linear-gradient(135deg, var(--accent-green), rgba(52, 199, 89, 0.8));
  border-color: var(--accent-green);
}

.glass-button.danger {
  background: linear-gradient(135deg, var(--accent-red), rgba(255, 59, 48, 0.8));
  border-color: var(--accent-red);
}

/* Additional Glass Effects */
.glass-card {
  background: var(--glass-bg-primary);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 20px;
  box-shadow:
    0 4px 24px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-input {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-primary);
  font-family: inherit;
  transition: all 0.2s ease;
}

.glass-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

.glass-input::placeholder {
  color: var(--text-secondary);
}

/* Status indicators */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-dot.online {
  background: var(--accent-green);
  box-shadow: 0 0 8px rgba(52, 199, 89, 0.5);
}

.status-dot.offline {
  background: var(--text-secondary);
}

.status-dot.active {
  background: var(--accent-blue);
  box-shadow: 0 0 8px rgba(0, 122, 255, 0.5);
}
