:root {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* True Liquid Glass Transparency - Semi-transparent */
  --glass-primary: rgba(255, 255, 255, 0.15);
  --glass-secondary: rgba(255, 255, 255, 0.2);
  --glass-tertiary: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-hover: rgba(255, 255, 255, 0.3);
  --glass-shadow-light: rgba(255, 255, 255, 0.1);
  --glass-shadow-dark: rgba(0, 0, 0, 0.1);

  --text-primary: rgba(0, 0, 0, 0.9);
  --text-secondary: rgba(0, 0, 0, 0.7);
  --text-tertiary: rgba(0, 0, 0, 0.5);

  --accent-primary: #0A84FF;
  --accent-success: #30D158;
  --accent-warning: #FF9F0A;
  --accent-danger: #FF453A;

  --background-base: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.08) 0%, rgba(240, 240, 255, 0.05) 50%, rgba(255, 255, 255, 0.03) 100%);
  --background-overlay: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(240, 240, 255, 0.01) 100%);

  color-scheme: light;
  color: var(--text-primary);
  background: var(--background-base);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
  background: transparent;
  position: relative;
  border-radius: 20px;
}

html {
  border-radius: 20px;
  overflow: hidden;
  background: transparent;
}

#root {
  border-radius: 20px;
  overflow: hidden;
  width: 100%;
  height: 100vh;
  background: transparent;
  -webkit-app-region: no-drag;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* True Liquid Glass Components */
.glass-container {
  background: transparent;
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
}

.glass-container:hover {
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-1px);
}

.glass-button {
  background: linear-gradient(135deg, var(--glass-primary) 0%, var(--glass-secondary) 100%);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 14px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  font-family: inherit;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.01em;
}

.glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.glass-button:hover {
  background: linear-gradient(135deg, var(--glass-secondary) 0%, var(--glass-tertiary) 100%);
  border-color: var(--glass-border-hover);
  transform: translateY(-0.5px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.glass-button.primary {
  background: linear-gradient(135deg, var(--accent-primary), rgba(10, 132, 255, 0.8));
  border-color: var(--accent-primary);
  color: white;
}

.glass-button.success {
  background: linear-gradient(135deg, var(--accent-success), rgba(48, 209, 88, 0.8));
  border-color: var(--accent-success);
  color: white;
}

.glass-button.danger {
  background: linear-gradient(135deg, var(--accent-danger), rgba(255, 69, 58, 0.8));
  border-color: var(--accent-danger);
  color: white;
}

/* Additional Glass Effects */
.glass-card {
  background: var(--glass-bg-primary);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 20px;
  box-shadow:
    0 4px 24px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-input {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-primary);
  font-family: inherit;
  transition: all 0.2s ease;
}

.glass-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

.glass-input::placeholder {
  color: var(--text-secondary);
}

/* Status indicators */
.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  flex-shrink: 0;
}

.status-dot::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

.status-dot.active {
  background: var(--accent-success);
  box-shadow: 0 0 12px rgba(48, 209, 88, 0.4);
}

.status-dot.active::before {
  background: var(--accent-success);
}

.status-dot.offline {
  background: var(--text-tertiary);
  opacity: 0.6;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}
