import { ipcRenderer, contextBridge } from 'electron'

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('ipcRenderer', {
  on(...args: Parameters<typeof ipcRenderer.on>) {
    const [channel, listener] = args
    return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
  },
  off(...args: Parameters<typeof ipcRenderer.off>) {
    const [channel, ...omit] = args
    return ipcRenderer.off(channel, ...omit)
  },
  send(...args: Parameters<typeof ipcRenderer.send>) {
    const [channel, ...omit] = args
    return ipcRenderer.send(channel, ...omit)
  },
  invoke(...args: Parameters<typeof ipcRenderer.invoke>) {
    const [channel, ...omit] = args
    return ipcRenderer.invoke(channel, ...omit)
  },

  // You can expose other APTs you need here.
  // ...
})

// Expose window resize API and Roblox functions
contextBridge.exposeInMainWorld('electronAPI', {
  resizeWindow: (width: number, height: number) => {
    ipcRenderer.send('resize-window', { width, height })
  },

  // Roblox detection and anti-AFK functions
  getRobloxInstances: () => ipcRenderer.invoke('get-roblox-instances'),
  startAntiAfk: (instanceId: string) => ipcRenderer.invoke('start-anti-afk', instanceId),
  stopAntiAfk: (instanceId: string) => ipcRenderer.invoke('stop-anti-afk', instanceId),
  toggleAntiAfk: (instanceId: string) => ipcRenderer.invoke('toggle-anti-afk', instanceId),
  onRobloxInstancesUpdated: (callback: Function) => ipcRenderer.on('roblox-instances-updated', (_event, instances) => callback(instances)),
})
