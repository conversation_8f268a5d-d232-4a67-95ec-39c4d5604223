#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  border-radius: 20px;
  background: transparent;
  -webkit-clip-path: inset(0 round 20px);
  clip-path: inset(0 round 20px);
}

/* Force the entire window to be rounded */
html {
  -webkit-clip-path: inset(0 round 20px) !important;
  clip-path: inset(0 round 20px) !important;
}

body {
  -webkit-clip-path: inset(0 round 20px) !important;
  clip-path: inset(0 round 20px) !important;
}

.app {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 50%, #cbd5e0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', sans-serif;
  padding: 20px;
  position: relative;
  animation: slideInFromTop 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 20px;
  -webkit-clip-path: inset(0 round 20px);
  clip-path: inset(0 round 20px);
}

.app::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 32px;
  width: 100%;
  max-width: 420px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.5) inset,
    0 -1px 0 rgba(0, 0, 0, 0.05) inset;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  position: relative;
}

.logo {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.header h1 {
  color: #1a202c;
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.5px;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.app-header {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  -webkit-app-region: drag;
  cursor: move;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.app-logo {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  flex-shrink: 0;
}

.app-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.85rem;
  justify-content: center;
  flex: 1;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.stat-divider {
  color: var(--text-tertiary);
  font-size: 0.7rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.master-toggle {
  min-width: 120px;
}

.app-content {
  flex: 1;
  min-height: 0;
}

/* Control Panel */
.control-panel {
  padding: 20px;
  background: transparent !important;
  -webkit-app-region: no-drag;
}

.control-section {
  margin-bottom: 16px;
}

.control-label {
  display: block;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Dropdown Styles */
.dropdown-container {
  position: relative;
}

.dropdown-trigger {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 16px;
  padding: 16px 20px;
  color: #2d3748;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

.dropdown-trigger:hover {
  background: rgba(255, 255, 255, 0.85);
  transform: translateY(-1px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.7) inset;
}

.dropdown-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-instance {
  display: flex;
  align-items: center;
  gap: 12px;
}

.placeholder {
  color: var(--text-tertiary);
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #718096;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(40px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 16px;
  overflow: hidden;
  z-index: 1000;
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
  animation: dropdownSlide 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-menu::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  height: 50px; /* Fixed height for exactly 3 items to fit in 150px */
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.dropdown-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.dropdown-item.disabled:hover {
  background: transparent;
}

.dropdown-item.selected {
  background: rgba(10, 132, 255, 0.1);
  border-color: rgba(10, 132, 255, 0.2);
}

.dropdown-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dropdown-item-name {
  font-weight: 600;
  font-size: 1rem;
}

.dropdown-item-meta {
  font-size: 0.85rem;
  color: var(--text-tertiary);
}

.dropdown-item-status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dropdown-item-status.on {
  background: rgba(48, 209, 88, 0.15);
  color: var(--accent-success);
}

.dropdown-item-status.off {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-tertiary);
}

/* Instance List Styles */
.instances-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 240px;
  overflow-y: auto;
  padding-right: 4px;
}

.instances-list::-webkit-scrollbar {
  width: 4px;
}

.instances-list::-webkit-scrollbar-track {
  background: transparent;
}

.instances-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.instance-item {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

.instance-item:hover {
  background: rgba(255, 255, 255, 0.75);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.7) inset;
}

.instance-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.instance-name {
  color: #2d3748;
  font-size: 15px;
  font-weight: 600;
  letter-spacing: -0.2px;
}

.instance-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.status-dot.active {
  background: #48bb78;
  box-shadow:
    0 0 0 3px rgba(72, 187, 120, 0.2),
    0 0 8px rgba(72, 187, 120, 0.4);
}

.status-dot.inactive {
  background: #a0aec0;
  box-shadow: 0 0 0 3px rgba(160, 174, 192, 0.1);
}

.toggle-button {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 8px 16px;
  color: #2d3748;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
  min-width: 48px;
  text-align: center;
}

.toggle-button:hover {
  background: rgba(255, 255, 255, 0.85);
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.7) inset;
}

.toggle-button:active {
  transform: translateY(0);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

/* Action Section */
.action-section {
  display: flex;
  justify-content: center;
  padding-top: 4px;
}

.main-action-button {
  min-width: 100px;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.main-action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.main-action-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.main-action-button:hover::before {
  left: 100%;
}

.main-action-button:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.main-action-button.on {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: var(--text-primary);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.main-action-button.on:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.main-action-button.off {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.main-action-button.off:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}
