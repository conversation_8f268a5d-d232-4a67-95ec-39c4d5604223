#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 24px;
  background: transparent;
}

.app-header {
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 32px;
}

.app-title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-logo {
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.app-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, rgba(255, 255, 255, 0.7) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.app-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 0.9rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.stat-divider {
  color: var(--text-tertiary);
  font-size: 0.8rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.master-toggle {
  min-width: 120px;
}

.app-content {
  flex: 1;
  min-height: 0;
}

/* Control Panel */
.control-panel {
  padding: 32px;
  background: transparent !important;
}

.control-section {
  margin-bottom: 24px;
}

.control-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Dropdown Styles */
.dropdown-container {
  position: relative;
}

.dropdown-trigger {
  width: 100%;
  background: transparent;
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  font-size: 1rem;
}

.dropdown-trigger:hover {
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.dropdown-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-instance {
  display: flex;
  align-items: center;
  gap: 12px;
}

.placeholder {
  color: var(--text-tertiary);
}

.dropdown-arrow {
  transition: transform 0.2s ease;
  color: var(--text-secondary);
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(40px) saturate(200%);
  -webkit-backdrop-filter: blur(40px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin-top: 8px;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.dropdown-item.selected {
  background: rgba(10, 132, 255, 0.1);
  border-color: rgba(10, 132, 255, 0.2);
}

.dropdown-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dropdown-item-name {
  font-weight: 600;
  font-size: 1rem;
}

.dropdown-item-meta {
  font-size: 0.85rem;
  color: var(--text-tertiary);
}

.dropdown-item-status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dropdown-item-status.on {
  background: rgba(48, 209, 88, 0.15);
  color: var(--accent-success);
}

.dropdown-item-status.off {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-tertiary);
}

/* Action Section */
.action-section {
  display: flex;
  justify-content: center;
  padding-top: 8px;
}

.main-action-button {
  min-width: 120px;
  font-size: 1rem;
  font-weight: 600;
}
