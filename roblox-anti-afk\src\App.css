#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.app-container {
  width: 100%;
  height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  box-sizing: border-box;
}

.app-header {
  flex-shrink: 0;
}

.header-card {
  text-align: center;
  padding: 24px;
}

.app-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, var(--text-primary), var(--accent-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0;
}

.controls-section {
  flex-shrink: 0;
}

.controls-card {
  padding: 20px;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.controls-header h2 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
}

.global-control {
  display: flex;
  justify-content: center;
}

.global-toggle {
  font-size: 16px;
  padding: 16px 32px;
  min-width: 200px;
}

.instances-section {
  flex: 1;
  min-height: 0;
}

.instances-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.instances-title {
  margin: 0 0 16px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
}

.instances-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-right: 8px;
}

.instances-list::-webkit-scrollbar {
  width: 6px;
}

.instances-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.instances-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.instances-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.instance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin: 0;
}

.instance-info {
  flex: 1;
}

.instance-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.instance-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.instance-pid {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-left: auto;
}

.instance-status {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.status-text {
  font-weight: 500;
}

.status-text.online {
  color: var(--accent-green);
}

.status-text.active {
  color: var(--accent-blue);
}

.status-text.offline {
  color: var(--text-secondary);
}

.instance-controls {
  margin-left: 16px;
}

.no-instances {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--text-secondary);
}

.no-instances p {
  margin: 8px 0;
}

.hint {
  font-size: 0.9rem;
  opacity: 0.8;
}
