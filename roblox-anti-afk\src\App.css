#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 24px;
}

.app-header {
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 32px;
}

.app-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, rgba(255, 255, 255, 0.7) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.app-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 0.9rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.stat-divider {
  color: var(--text-tertiary);
  font-size: 0.8rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.scan-button {
  min-width: 80px;
}

.master-toggle {
  min-width: 100px;
}

.app-content {
  flex: 1;
  min-height: 0;
}

.instances-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 32px;
}

.instances-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.instances-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.instances-count {
  font-size: 0.9rem;
  color: var(--text-secondary);
  background: var(--glass-primary);
  padding: 6px 12px;
  border-radius: 12px;
  border: 1px solid var(--glass-border);
}

.instances-grid {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  padding-right: 8px;
}

.instances-grid::-webkit-scrollbar {
  width: 4px;
}

.instances-grid::-webkit-scrollbar-track {
  background: transparent;
}

.instances-grid::-webkit-scrollbar-thumb {
  background: var(--glass-border);
  border-radius: 2px;
}

.instances-grid::-webkit-scrollbar-thumb:hover {
  background: var(--glass-border-hover);
}

.instance-card {
  background: var(--glass-primary);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.instance-card:hover {
  background: var(--glass-secondary);
  border-color: var(--glass-border-hover);
  transform: translateY(-1px);
}

.instance-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.instance-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.instance-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.instance-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
  line-height: 1.2;
}

.instance-meta {
  font-size: 0.85rem;
  color: var(--text-tertiary);
  font-weight: 400;
}

.instance-status {
  flex-shrink: 0;
}

.status-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.online {
  background: rgba(48, 209, 88, 0.15);
  color: var(--accent-success);
  border: 1px solid rgba(48, 209, 88, 0.2);
}

.status-badge.active {
  background: rgba(10, 132, 255, 0.15);
  color: var(--accent-primary);
  border: 1px solid rgba(10, 132, 255, 0.2);
}

.status-badge.offline {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-tertiary);
  border: 1px solid var(--glass-border);
}

.instance-actions {
  display: flex;
  justify-content: flex-end;
}

.instance-toggle {
  min-width: 80px;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 16px;
  padding: 48px 24px;
}

.empty-icon {
  font-size: 4rem;
  color: var(--text-tertiary);
  opacity: 0.5;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.empty-subtitle {
  font-size: 0.9rem;
  color: var(--text-tertiary);
  max-width: 300px;
  line-height: 1.5;
}
