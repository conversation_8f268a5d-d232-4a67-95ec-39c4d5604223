#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
  background: transparent;
}

.app-header {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 12px 16px;
  -webkit-app-region: drag;
  cursor: move;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.app-logo {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  flex-shrink: 0;
}

.app-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.85rem;
  justify-content: center;
  flex: 1;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.stat-divider {
  color: var(--text-tertiary);
  font-size: 0.7rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.master-toggle {
  min-width: 120px;
}

.app-content {
  flex: 1;
  min-height: 0;
}

/* Control Panel */
.control-panel {
  padding: 20px;
  background: transparent !important;
  -webkit-app-region: no-drag;
}

.control-section {
  margin-bottom: 16px;
}

.control-label {
  display: block;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Dropdown Styles */
.dropdown-container {
  position: relative;
}

.dropdown-trigger {
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.dropdown-trigger:hover {
  background: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-1px);
}

.dropdown-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-instance {
  display: flex;
  align-items: center;
  gap: 12px;
}

.placeholder {
  color: var(--text-tertiary);
}

.dropdown-arrow {
  transition: transform 0.2s ease;
  color: var(--text-secondary);
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(40px) saturate(200%);
  -webkit-backdrop-filter: blur(40px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  margin-top: 8px;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.dropdown-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.dropdown-item.selected {
  background: rgba(10, 132, 255, 0.1);
  border-color: rgba(10, 132, 255, 0.2);
}

.dropdown-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dropdown-item-name {
  font-weight: 600;
  font-size: 1rem;
}

.dropdown-item-meta {
  font-size: 0.85rem;
  color: var(--text-tertiary);
}

.dropdown-item-status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dropdown-item-status.on {
  background: rgba(48, 209, 88, 0.15);
  color: var(--accent-success);
}

.dropdown-item-status.off {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-tertiary);
}

/* Action Section */
.action-section {
  display: flex;
  justify-content: center;
  padding-top: 4px;
}

.main-action-button {
  min-width: 100px;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 12px 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.main-action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.main-action-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border-color: rgba(255, 255, 255, 0.7);
  transform: translateY(-1px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.main-action-button:hover::before {
  left: 100%;
}

.main-action-button:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.main-action-button.on {
  background: linear-gradient(135deg, var(--accent-success) 0%, rgba(48, 209, 88, 0.8) 100%);
  border-color: var(--accent-success);
  color: white;
  box-shadow:
    0 4px 16px rgba(48, 209, 88, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.main-action-button.on:hover {
  background: linear-gradient(135deg, rgba(48, 209, 88, 1.1) 0%, var(--accent-success) 100%);
  box-shadow:
    0 6px 20px rgba(48, 209, 88, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}
