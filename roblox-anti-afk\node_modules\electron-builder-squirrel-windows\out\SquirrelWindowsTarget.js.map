{"version": 3, "file": "SquirrelWindowsTarget.js", "sourceRoot": "", "sources": ["../src/SquirrelWindowsTarget.ts"], "names": [], "mappings": ";;AAAA,gEAAoE;AACpE,+CAA8E;AAC9E,iEAA+D;AAC/D,qDAAqF;AAErF,6BAA4B;AAC5B,iDAAiF;AAEjF,MAAqB,qBAAsB,SAAQ,wBAAM;IAIvD,YACmB,QAAqB,EAC7B,MAAc;QAEvB,KAAK,CAAC,UAAU,CAAC,CAAA;QAHA,aAAQ,GAAR,QAAQ,CAAa;QAC7B,WAAM,GAAN,MAAM,CAAQ;QALzB,2DAA2D;QAClD,YAAO,GAA2B,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,EAA4B,CAAA;IAO/J,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAA;QACxC,MAAM,aAAa,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAEpD,uDAAuD;QACvD,MAAM,SAAS,GAAG,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,wCAAwC,CAAC,CAAA;QACzH,MAAM,WAAW,GAAG,GAAG,aAAa,IAAI,IAAA,6BAAc,EAAC,OAAO,CAAC,aAAa,CAAA;QAE5E,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,mBAAmB,IAAA,+BAAa,EAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACxF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,CAAA;QAE1D,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,kBAAkB;YACzC,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,IAAI,IAAI,KAAK,sBAAI,CAAC,IAAI,EAAE,CAAC;YACvB,kBAAG,CAAC,IAAI,CAAC,+JAA+J,CAAC,CAAA;QAC3K,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAC5D,MAAM,eAAe,GAAG,IAAI,8BAAe,CAAC,WAAW,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAA;QACnF,MAAM,eAAe,CAAC,cAAc,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAE9F,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,gBAAgB,EAAE,GAAG,aAAa,UAAU,OAAO,GAAG,IAAA,+BAAa,EAAC,IAAI,CAAC,MAAM;YAC/E,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAA;QAEF,MAAM,aAAa,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,IAAA,6BAAc,EAAC,OAAO,CAAC,GAAG,CAAA;QACnE,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC;YACpC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,aAAa,YAAY,CAAC;YAC9D,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;SACT,CAAC,CAAA;QACF,IAAI,WAAW,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YACvC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC;gBACpC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,aAAa,aAAa,CAAC;gBAC/D,MAAM,EAAE,IAAI;gBACZ,IAAI;gBACJ,QAAQ;aACT,CAAC,CAAA;QACJ,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC;YACpC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC;YAC5C,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;SACT,CAAC,CAAA;IACJ,CAAC;IAED,IAAY,OAAO;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAA;IACxD,CAAC;IAED,KAAK,CAAC,2BAA2B;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QAClC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAA;YAC/C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,OAAO,GAAG,sBAAsB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,gBAAgB,QAAQ,CAAC,IAAI,CAAC,6BAA6B,oBAAoB,CAAA;YAC1I,CAAC;YAED,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAI,wCAAyB,CACjC,+IAA+I,CAChJ,CAAA;YACH,CAAC;QACH,CAAC;QAED,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAErC,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAChC,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,iBAAiB,EAAE,CAAA;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,OAAO,GAAoB;YAC/B,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW;YACrD,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO;YACvD,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,uEAAuE;YACvE,OAAO,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;YAClC,OAAO;YACP,kBAAkB,EAAE,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB,UAAU,eAAe;YAC9F,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,uBAAuB,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,QAAQ,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAQ,EAAE,EAAE,CAAC;YAC1I,UAAU,EAAE,MAAM,IAAA,2BAAa,EAAC,kBAAkB,EAAE,OAAO,EAAE,0FAA0F,CAAC;YACxJ,GAAI,IAAI,CAAC,OAAe;SACzB,CAAA;QAED,IAAI,IAAA,8BAAe,EAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QAC3C,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA;QACxE,CAAC;QAED,IAAI,CAAC,CAAC,YAAY,IAAI,OAAO,CAAC,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAA;YAChD,IAAI,YAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBACjD,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,CAAA;YACnF,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAA;YAC/C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,kBAAG,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAA;YACxE,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,cAAc,GAAG,sBAAsB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAA;gBAC1E,kBAAG,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,EAAE,uBAAuB,CAAC,CAAA;YAC/E,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;CACF;AAzID,wCAyIC;AAED,SAAS,uBAAuB,CAAC,OAAY;IAC3C,KAAK,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE,cAAc,EAAE,KAAK,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,UAAU,CAAC,EAAE,CAAC;QACpK,IAAI,IAAI,IAAI,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,wCAAyB,CAAC,UAAU,IAAI,iCAAiC,CAAC,CAAA;QACtF,CAAC;IACH,CAAC;IAED,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;QACvB,kBAAG,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAA;QACrG,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAA;IAC9B,CAAC;IAED,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;IACvB,IAAI,GAAG,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE,CAAC;QAC5C,MAAM,IAAI,wCAAyB,CAAC,kDAAkD,GAAG,kBAAkB,CAAC,CAAA;IAC9G,CAAC;AACH,CAAC", "sourcesContent": ["import { sanitizeFileName } from \"app-builder-lib/out/util/filename\"\nimport { InvalidConfigurationError, log, isEmptyOrSpaces } from \"builder-util\"\nimport { getBinFromUrl } from \"app-builder-lib/out/binDownload\"\nimport { Arch, getArchSuffix, SquirrelWindowsOptions, Target } from \"app-builder-lib\"\nimport { WinPackager } from \"app-builder-lib/out/winPackager\"\nimport * as path from \"path\"\nimport { convertVersion, SquirrelBuilder, SquirrelOptions } from \"./squirrelPack\"\n\nexport default class SquirrelWindowsTarget extends Target {\n  //tslint:disable-next-line:no-object-literal-type-assertion\n  readonly options: SquirrelWindowsOptions = { ...this.packager.platformSpecificBuildOptions, ...this.packager.config.squirrelWindows } as SquirrelWindowsOptions\n\n  constructor(\n    private readonly packager: WinPackager,\n    readonly outDir: string\n  ) {\n    super(\"squirrel\")\n  }\n\n  async build(appOutDir: string, arch: Arch) {\n    const packager = this.packager\n    const version = packager.appInfo.version\n    const sanitizedName = sanitizeFileName(this.appName)\n\n    // tslint:disable-next-line:no-invalid-template-strings\n    const setupFile = packager.expandArtifactNamePattern(this.options, \"exe\", arch, \"${productName} Setup ${version}.${ext}\")\n    const packageFile = `${sanitizedName}-${convertVersion(version)}-full.nupkg`\n\n    const installerOutDir = path.join(this.outDir, `squirrel-windows${getArchSuffix(arch)}`)\n    const artifactPath = path.join(installerOutDir, setupFile)\n\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"Squirrel.Windows\",\n      file: artifactPath,\n      arch,\n    })\n\n    if (arch === Arch.ia32) {\n      log.warn(\"For windows consider only distributing 64-bit or use nsis target, see https://github.com/electron-userland/electron-builder/issues/359#issuecomment-214851130\")\n    }\n\n    const distOptions = await this.computeEffectiveDistOptions()\n    const squirrelBuilder = new SquirrelBuilder(distOptions, installerOutDir, packager)\n    await squirrelBuilder.buildInstaller({ setupFile, packageFile }, appOutDir, this.outDir, arch)\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      target: this,\n      arch,\n      safeArtifactName: `${sanitizedName}-Setup-${version}${getArchSuffix(arch)}.exe`,\n      packager: this.packager,\n    })\n\n    const packagePrefix = `${this.appName}-${convertVersion(version)}-`\n    packager.info.dispatchArtifactCreated({\n      file: path.join(installerOutDir, `${packagePrefix}full.nupkg`),\n      target: this,\n      arch,\n      packager,\n    })\n    if (distOptions.remoteReleases != null) {\n      packager.info.dispatchArtifactCreated({\n        file: path.join(installerOutDir, `${packagePrefix}delta.nupkg`),\n        target: this,\n        arch,\n        packager,\n      })\n    }\n\n    packager.info.dispatchArtifactCreated({\n      file: path.join(installerOutDir, \"RELEASES\"),\n      target: this,\n      arch,\n      packager,\n    })\n  }\n\n  private get appName() {\n    return this.options.name || this.packager.appInfo.name\n  }\n\n  async computeEffectiveDistOptions(): Promise<SquirrelOptions> {\n    const packager = this.packager\n    let iconUrl = this.options.iconUrl\n    if (iconUrl == null) {\n      const info = await packager.info.repositoryInfo\n      if (info != null) {\n        iconUrl = `https://github.com/${info.user}/${info.project}/blob/master/${packager.info.relativeBuildResourcesDirname}/icon.ico?raw=true`\n      }\n\n      if (iconUrl == null) {\n        throw new InvalidConfigurationError(\n          \"squirrelWindows.iconUrl is not specified, please see https://www.electron.build/configuration/squirrel-windows#SquirrelWindowsOptions-iconUrl\"\n        )\n      }\n    }\n\n    checkConflictingOptions(this.options)\n\n    const appInfo = packager.appInfo\n    const projectUrl = await appInfo.computePackageUrl()\n    const appName = this.appName\n    const options: SquirrelOptions = {\n      name: appName,\n      productName: this.options.name || appInfo.productName,\n      appId: this.options.useAppIdAsId ? appInfo.id : appName,\n      version: appInfo.version,\n      description: appInfo.description,\n      // better to explicitly set to empty string, to avoid any nugget errors\n      authors: appInfo.companyName || \"\",\n      iconUrl,\n      extraMetadataSpecs: projectUrl == null ? null : `\\n    <projectUrl>${projectUrl}</projectUrl>`,\n      copyright: appInfo.copyright,\n      packageCompressionLevel: parseInt((process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL || packager.compression === \"store\" ? 0 : 9) as any, 10),\n      vendorPath: await getBinFromUrl(\"Squirrel.Windows\", \"1.9.0\", \"zJHk4CMATM7jHJ2ojRH1n3LkOnaIezDk5FAzJmlSEQSiEdRuB4GGLCegLDtsRCakfHIVfKh3ysJHLjynPkXwhQ==\"),\n      ...(this.options as any),\n    }\n\n    if (isEmptyOrSpaces(options.description)) {\n      options.description = options.productName\n    }\n\n    if (options.remoteToken == null) {\n      options.remoteToken = process.env.GH_TOKEN || process.env.GITHUB_TOKEN\n    }\n\n    if (!(\"loadingGif\" in options)) {\n      const resourceList = await packager.resourceList\n      if (resourceList.includes(\"install-spinner.gif\")) {\n        options.loadingGif = path.join(packager.buildResourcesDir, \"install-spinner.gif\")\n      }\n    }\n\n    if (this.options.remoteReleases === true) {\n      const info = await packager.info.repositoryInfo\n      if (info == null) {\n        log.warn(\"remoteReleases set to true, but cannot get repository info\")\n      } else {\n        options.remoteReleases = `https://github.com/${info.user}/${info.project}`\n        log.info({ remoteReleases: options.remoteReleases }, `remoteReleases is set`)\n      }\n    }\n\n    return options\n  }\n}\n\nfunction checkConflictingOptions(options: any) {\n  for (const name of [\"outputDirectory\", \"appDirectory\", \"exe\", \"fixUpPaths\", \"usePackageJson\", \"extraFileSpecs\", \"extraMetadataSpecs\", \"skipUpdateIcon\", \"setupExe\"]) {\n    if (name in options) {\n      throw new InvalidConfigurationError(`Option ${name} is ignored, do not specify it.`)\n    }\n  }\n\n  if (\"noMsi\" in options) {\n    log.warn(`noMsi is deprecated, please specify as \"msi\": true if you want to create an MSI installer`)\n    options.msi = !options.noMsi\n  }\n\n  const msi = options.msi\n  if (msi != null && typeof msi !== \"boolean\") {\n    throw new InvalidConfigurationError(`msi expected to be boolean value, but string '\"${msi}\"' was specified`)\n  }\n}\n"]}